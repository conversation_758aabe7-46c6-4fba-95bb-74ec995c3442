generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model TradingAccounts {
  id                   String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                 String              @db.VarChar(100)
  initialCapital       Decimal             @db.Decimal(20, 8)
  currentBalance       Decimal             @db.Decimal(20, 8)
  availableBalance     Decimal             @db.Decimal(20, 8)
  totalPnl             Decimal             @default(0) @db.Decimal(20, 8)
  maxDrawdown          Decimal             @default(0) @db.Decimal(5, 4)
  dailyPnl             Decimal             @default(0) @db.Decimal(20, 8)
  isActive             Boolean             @default(true)
  riskSettings         Json
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @default(now()) @updatedAt
  accountType          String              @default("SIMULATION") @db.VarChar(20)
  apiCredentials       Json?
  executionEngine      String              @default("simulation") @db.Var<PERSON>har(50)
  parentAccountId      String?             @db.Uuid
  syncSettings         Json?
  userId               String              @db.Uuid
  createdBy            String?             @db.VarChar(255)
  updatedBy            String?             @db.VarChar(255)
  RiskAssessments      RiskAssessments[]
  RiskEvents           RiskEvents[]
  TradingAccounts      TradingAccounts?    @relation("TradingAccountsToTradingAccounts", fields: [parentAccountId], references: [id])
  otherTradingAccounts TradingAccounts[]   @relation("TradingAccountsToTradingAccounts")
  Users                Users               @relation(fields: [userId], references: [id], onDelete: Cascade)
  TradingOrders        TradingOrders[]
  TradingPositions     TradingPositions[]
  TradingStatistics    TradingStatistics[]

  @@index([accountType])
  @@index([executionEngine])
  @@index([parentAccountId])
  @@index([userId])
}

model TradingOrders {
  id               String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  positionId       String?           @db.Uuid
  accountId        String            @db.Uuid
  symbolId         String            @db.Uuid
  orderType        String            @db.VarChar(20)
  side             String            @db.VarChar(10)
  quantity         Decimal           @db.Decimal(20, 8)
  price            Decimal?          @db.Decimal(20, 8)
  stopPrice        Decimal?          @db.Decimal(20, 8)
  status           String            @default("PENDING") @db.VarChar(20)
  exchangeOrderId  String?           @db.VarChar(100)
  filledQuantity   Decimal           @default(0) @db.Decimal(20, 8)
  averagePrice     Decimal?          @db.Decimal(20, 8)
  commission       Decimal           @default(0) @db.Decimal(20, 8)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @default(now()) @updatedAt
  filledAt         DateTime?
  engineMetadata   Json?
  executionSource  String            @default("SIMULATION") @db.VarChar(20)
  TradingAccounts  TradingAccounts   @relation(fields: [accountId], references: [id], onDelete: Cascade)
  TradingPositions TradingPositions? @relation(fields: [positionId], references: [id], onDelete: Cascade)
  symbols          Symbols           @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([accountId, status])
  @@index([createdAt])
  @@index([exchangeOrderId])
  @@index([executionSource])
  @@index([symbolId, status])
  @@index([status, createdAt], map: "idx_trading_orders_status_time")
}

model TradingPositions {
  id                    String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountId             String             @db.Uuid
  symbolId              String             @db.Uuid
  signalId              String?            @db.VarChar(100)
  side                  String             @db.VarChar(10)
  entryPrice            Decimal            @db.Decimal(20, 8)
  quantity              Decimal            @db.Decimal(20, 8)
  leverage              Int                @default(10)
  marginUsed            Decimal            @db.Decimal(20, 8)
  stopLoss              Decimal?           @db.Decimal(20, 8)
  takeProfit            Decimal?           @db.Decimal(20, 8)
  currentPrice          Decimal?           @db.Decimal(20, 8)
  unrealizedPnl         Decimal            @default(0) @db.Decimal(20, 8)
  realizedPnl           Decimal            @default(0) @db.Decimal(20, 8)
  status                String             @default("OPEN") @db.VarChar(20)
  pyramidLevel          Int                @default(1)
  atrValue              Decimal?           @db.Decimal(20, 8)
  openedAt              DateTime           @default(now())
  closedAt              DateTime?
  metadata              Json?
  executionSource       String             @default("SIMULATION") @db.VarChar(20)
  syncedFromId          String?            @db.Uuid
  createdBy             String?            @db.VarChar(255)
  updatedBy             String?            @db.VarChar(255)
  TradingOrders         TradingOrders[]
  TradingAccounts       TradingAccounts    @relation(fields: [accountId], references: [id], onDelete: Cascade)
  symbols               Symbols            @relation(fields: [symbolId], references: [id], onDelete: Cascade)
  TradingPositions      TradingPositions?  @relation("TradingPositionsToTradingPositions", fields: [syncedFromId], references: [id])
  otherTradingPositions TradingPositions[] @relation("TradingPositionsToTradingPositions")

  @@index([accountId, status])
  @@index([executionSource])
  @@index([signalId])
  @@index([symbolId, status])
  @@index([syncedFromId])
  @@index([accountId, symbolId], map: "idx_trading_positions_account_symbol")
}

model TradingSignals {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId                  String                      @db.Uuid
  signalType                String                      @db.VarChar(10)
  strength                  Int
  reasoning                 String[]
  keyFactors                String[]
  riskWarning               String
  positionSize              Decimal                     @db.Decimal(5, 4)
  stopLoss                  Decimal?                    @db.Decimal(20, 8)
  takeProfit                Decimal?                    @db.Decimal(20, 8)
  timeHorizon               String                      @db.VarChar(20)
  confidence                Decimal                     @db.Decimal(3, 2)
  marketContext             String
  qualityScore              Decimal                     @db.Decimal(3, 2)
  validationStatus          String                      @default("pending") @db.VarChar(20)
  executionStatus           String                      @default("pending") @db.VarChar(20)
  executedAt                DateTime?
  actualReturn              Decimal?                    @db.Decimal(8, 4)
  performanceScore          Decimal?                    @db.Decimal(3, 2)
  timestamp                 DateTime
  createdAt                 DateTime                    @default(now())
  updatedAt                 DateTime                    @default(now()) @updatedAt
  fundamentalSignal         Json?
  fusionWeights             Json?
  generationMethod          String?                     @default("FOUR_DIMENSIONAL") @db.VarChar(30)
  quantitativeSignal        Json?
  sentimentSignal           Json?
  signalComponents          Json?
  signalTypeV2              String?                     @default("FUSED") @db.VarChar(20)
  technicalSignal           Json?
  AiReasoningChains         AiReasoningChains[]
  InvestmentDecisions       InvestmentDecisions[]
  SignalPerformanceTracking SignalPerformanceTracking[]
  SignalQualityAssessments  SignalQualityAssessments[]
  Symbols                   Symbols                     @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([confidence])
  @@index([qualityScore(sort: Desc)])
  @@index([signalType, strength])
  @@index([symbolId, timestamp])
  @@index([confidence, createdAt], map: "idx_trading_signals_confidence")
  @@index([createdAt], map: "idx_trading_signals_created_at")
  @@index([symbolId, createdAt], map: "idx_trading_signals_symbol_time")
}

model AiCallLogs {
  id               String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  callType         String           @db.VarChar(50)
  modelName        String           @db.VarChar(50)
  promptHash       String           @db.VarChar(64)
  promptTemplateId String?          @db.Uuid
  inputData        Json
  marketContext    Json
  responseData     Json
  responseTimeMs   Int
  tokenUsage       Json
  costUsd          Decimal          @db.Decimal(10, 6)
  qualityScore     Decimal?         @db.Decimal(3, 2)
  confidenceScore  Decimal?         @db.Decimal(3, 2)
  validationResult String?          @db.VarChar(20)
  cacheKey         String?          @db.VarChar(128)
  cacheHit         Boolean          @default(false)
  cacheSourceId    String?          @db.Uuid
  requestId        String?          @db.VarChar(50)
  userSessionId    String?          @db.VarChar(50)
  apiEndpoint      String?          @db.VarChar(100)
  createdAt        DateTime         @default(now())
  scenario         String?          @db.VarChar(100)
  userConfigId     String?          @db.Uuid
  userId           String?          @db.VarChar(255)
  AiCallLogs       AiCallLogs?      @relation("AiCallLogsToAiCallLogs", fields: [cacheSourceId], references: [id])
  otherAiCallLogs  AiCallLogs[]     @relation("AiCallLogsToAiCallLogs")
  PromptTemplates  PromptTemplates? @relation(fields: [promptTemplateId], references: [id])

  @@index([cacheKey])
  @@index([callType, createdAt])
  @@index([costUsd])
  @@index([promptHash])
  @@index([qualityScore])
  @@index([responseTimeMs])
  @@index([validationResult, createdAt])
  @@index([costUsd, createdAt], map: "idx_ai_call_logs_cost_time")
  @@index([createdAt], map: "idx_ai_call_logs_created_at")
  @@index([marketContext], map: "idx_ai_call_logs_market_context_gin", type: Gin)
  @@index([modelName, callType], map: "idx_ai_call_logs_model_type")
  @@index([userId, createdAt], map: "idx_ai_call_logs_user_time")
}

model AiOptimizationSuggestions {
  id                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  suggestionType       String    @db.VarChar(50)
  priority             String    @db.VarChar(20)
  title                String    @db.VarChar(200)
  description          String
  analysisData         Json
  potentialSaving      Decimal?  @db.Decimal(10, 4)
  expectedImprovement  Decimal?  @db.Decimal(5, 4)
  implementationEffort String    @db.VarChar(20)
  status               String    @default("pending") @db.VarChar(20)
  implementedAt        DateTime?
  actualImprovement    Decimal?  @db.Decimal(5, 4)
  generatedBy          String    @db.VarChar(50)
  tags                 String[]  @default([])
  createdAt            DateTime  @default(now())
  updatedAt            DateTime  @default(now())

  @@index([potentialSaving])
  @@index([status, createdAt])
  @@index([suggestionType, priority])
}

model AiReasoningChains {
  id                  String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId            String               @db.Uuid
  reasoningType       String               @db.VarChar(30)
  contextData         Json
  reasoningSteps      Json
  inputData           Json
  intermediateResults Json?
  finalResult         Json
  modelName           String               @db.VarChar(50)
  modelVersion        String               @db.VarChar(20)
  promptTemplate      String?
  processingTimeMs    Int?
  tokenUsage          Json?
  costEstimate        Decimal?             @db.Decimal(10, 6)
  confidence          Decimal              @db.Decimal(3, 2)
  qualityScore        Decimal?             @db.Decimal(3, 2)
  validationStatus    String               @default("pending") @db.VarChar(20)
  relatedSignalId     String?              @db.Uuid
  relatedDecisionId   String?              @db.Uuid
  relatedRiskId       String?              @db.Uuid
  relatedTrendId      String?              @db.Uuid
  timestamp           DateTime
  createdAt           DateTime             @default(now())
  InvestmentDecisions InvestmentDecisions? @relation(fields: [relatedDecisionId], references: [id])
  RiskAssessments     RiskAssessments?     @relation(fields: [relatedRiskId], references: [id])
  TradingSignals      TradingSignals?      @relation(fields: [relatedSignalId], references: [id])
  TrendAnalyses       TrendAnalyses?       @relation(fields: [relatedTrendId], references: [id])
  Symbols             Symbols              @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([confidence])
  @@index([modelName, modelVersion])
  @@index([reasoningType, timestamp])
  @@index([symbolId, timestamp])
}

model ApiKeys {
  id         String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId     String    @db.Uuid
  keyHash    String    @unique @db.VarChar(255)
  name       String    @db.VarChar(100)
  scopes     Json      @default("[]")
  isActive   Boolean   @default(true)
  lastUsedAt DateTime?
  expiresAt  DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now())
  createdBy  String?   @db.VarChar(255)
  updatedBy  String?   @db.VarChar(255)
  Users      Users     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([isActive, expiresAt])
  @@index([keyHash])
  @@index([lastUsedAt])
  @@index([userId])
}

model ApiPerformanceMetrics {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  endpoint       String   @db.VarChar(100)
  method         String   @db.VarChar(10)
  responseTimeMs Int
  statusCode     Int
  errorMessage   String?
  requestSize    Int?
  responseSize   Int?
  userAgent      String?  @db.VarChar(200)
  ipAddress      String?  @db.Inet
  timestamp      DateTime
  createdAt      DateTime @default(now())

  @@index([endpoint, timestamp])
  @@index([responseTimeMs])
  @@index([statusCode, timestamp])
}

model CacheConfigurations {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  cacheType       String   @db.VarChar(30)
  cacheKeyPattern String   @db.VarChar(100)
  ttlSeconds      Int
  maxSize         Int?
  evictionPolicy  String   @default("LRU") @db.VarChar(20)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
}

model CachePerformanceMetrics {
  id                String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  metricDate        DateTime @unique @db.Date
  totalRequests     Int
  cacheHits         Int
  cacheMisses       Int
  cacheHitRate      Decimal  @db.Decimal(3, 2)
  costSaved         Decimal  @db.Decimal(10, 4)
  timeSavedMs       BigInt
  hitRateByType     Json
  costSavedByType   Json
  averageCacheAgeMs Int?
  invalidationRate  Decimal? @db.Decimal(3, 2)
  createdAt         DateTime @default(now())

  @@index([cacheHitRate])
  @@index([metricDate])
}

model ChartDataCache {
  id        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  cacheKey  String   @unique @db.VarChar(100)
  data      Json
  expiresAt DateTime
  hitCount  Int      @default(0)
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now())

  @@index([cacheKey])
  @@index([expiresAt])
  @@index([hitCount])
}

model ConfigItems {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  key            String   @unique @db.VarChar(100)
  value          String
  type           String   @db.VarChar(30)
  description    String?
  defaultValue   String?
  validationRule String?
  isSecret       Boolean  @default(false)
  lastUpdated    DateTime @default(now())
  version        Int      @default(1)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now())
  createdBy      String?  @db.VarChar(255)
  updatedBy      String?  @db.VarChar(255)

  @@index([lastUpdated])
  @@index([type])
}

model ConfigSnapshots {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  description String
  configs     String
  createdAt   DateTime @default(now())
  createdBy   String?  @db.VarChar(255)
  metadata    String?

  @@index([createdAt])
  @@index([createdBy])
}

model UserProfiles {
  id                String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId            String   @unique @db.Uuid
  riskTolerance     String   @db.VarChar(20)
  investmentHorizon String   @db.VarChar(20)
  tradingStyle      String   @db.VarChar(20)
  preferredAssets   Json?
  maxPositionSize   Decimal? @db.Decimal(10, 2)
  stopLossPercent   Decimal? @db.Decimal(5, 2)
  takeProfitPercent Decimal? @db.Decimal(5, 2)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              Users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
}

model AuditLog {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String?  @db.Uuid
  action       String   @db.VarChar(100)
  resource     String   @db.VarChar(100)
  resourceId   String?  @db.VarChar(255)
  oldValues    Json?
  newValues    Json?
  ipAddress    String?  @db.VarChar(45)
  userAgent    String?  @db.VarChar(500)
  timestamp    DateTime @default(now())
  success      Boolean  @default(true)
  errorMessage String?

  @@index([userId])
  @@index([action])
  @@index([resource])
  @@index([timestamp])
}

model UserPreferences {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId      String   @db.Uuid
  category    String   @db.VarChar(50)
  key         String   @db.VarChar(100)
  value       String
  dataType    String   @db.VarChar(20)
  isEncrypted Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  user        Users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, category, key])
  @@index([userId])
  @@index([category])
}

model ConfigHistory {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  key         String   @db.VarChar(100)
  oldValue    String?
  newValue    String
  changedBy   String?  @db.VarChar(255)
  changedAt   DateTime @default(now())
  changeType  String   @db.VarChar(20)
  description String?

  @@index([key])
  @@index([changedAt])
  @@index([changedBy])
}

model ConfigRollbackHistory {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  configKey    String   @db.VarChar(100)
  fromValue    String
  toValue      String
  rolledBackBy String?  @db.VarChar(255)
  rolledBackAt DateTime @default(now())
  reason       String?

  @@index([configKey])
  @@index([rolledBackAt])
}

model ConfigSnapshotRollbackHistory {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  snapshotId   String   @db.Uuid
  rolledBackBy String?  @db.VarChar(255)
  rolledBackAt DateTime @default(now())
  reason       String?

  @@index([snapshotId])
  @@index([rolledBackAt])
}

model UserConfigHistory {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId     String   @db.Uuid
  configKey  String   @db.VarChar(100)
  oldValue   String?
  newValue   String
  changedAt  DateTime @default(now())
  changeType String   @db.VarChar(20)
  user       Users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([configKey])
  @@index([changedAt])
}

model ReasoningSessions {
  id        String               @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sessionId String               @unique @db.VarChar(100)
  userId    String?              @db.Uuid
  context   Json
  startTime DateTime             @default(now())
  endTime   DateTime?
  status    String               @db.VarChar(20)
  metadata  Json?
  decisions ReasoningDecisions[]
  steps     ReasoningSteps[]

  @@index([sessionId])
  @@index([userId])
  @@index([startTime])
}

model ReasoningSteps {
  id         String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sessionId  String            @db.Uuid
  stepId     String            @db.VarChar(100)
  stepType   String            @db.VarChar(50)
  input      Json
  output     Json
  confidence Decimal           @db.Decimal(5, 4)
  timestamp  DateTime          @default(now())
  metadata   Json?
  session    ReasoningSessions @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@index([sessionId])
  @@index([stepId])
  @@index([timestamp])
}

model ReasoningDecisions {
  id           String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sessionId    String            @db.Uuid
  decisionId   String            @db.VarChar(100)
  decisionType String            @db.VarChar(50)
  options      Json
  chosen       Json
  reasoning    String
  confidence   Decimal           @db.Decimal(5, 4)
  timestamp    DateTime          @default(now())
  session      ReasoningSessions @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@index([sessionId])
  @@index([decisionId])
  @@index([timestamp])
}

model DecisionPaths {
  id          String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  pathId      String             @unique @db.VarChar(100)
  sessionId   String?            @db.Uuid
  startNode   String             @db.VarChar(100)
  endNode     String?            @db.VarChar(100)
  totalWeight Decimal            @db.Decimal(10, 4)
  isComplete  Boolean            @default(false)
  createdAt   DateTime           @default(now())
  metadata    Json?
  branches    DecisionBranches[]
  nodes       DecisionNodes[]

  @@index([pathId])
  @@index([sessionId])
  @@index([createdAt])
}

model DecisionNodes {
  id         String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  pathId     String        @db.Uuid
  nodeId     String        @db.VarChar(100)
  nodeType   String        @db.VarChar(50)
  data       Json
  confidence Decimal       @db.Decimal(5, 4)
  timestamp  DateTime      @default(now())
  path       DecisionPaths @relation(fields: [pathId], references: [id], onDelete: Cascade)

  @@index([pathId])
  @@index([nodeId])
}

model DecisionBranches {
  id          String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  pathId      String        @db.Uuid
  branchId    String        @db.VarChar(100)
  fromNode    String        @db.VarChar(100)
  toNode      String        @db.VarChar(100)
  weight      Decimal       @db.Decimal(10, 4)
  probability Decimal       @db.Decimal(5, 4)
  condition   Json?
  metadata    Json?
  path        DecisionPaths @relation(fields: [pathId], references: [id], onDelete: Cascade)

  @@index([pathId])
  @@index([branchId])
}

model AiPrediction {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  predictionId String   @unique @db.VarChar(100)
  modelName    String   @db.VarChar(100)
  inputData    Json
  prediction   Json
  confidence   Decimal  @db.Decimal(5, 4)
  timestamp    DateTime @default(now())
  metadata     Json?

  @@index([predictionId])
  @@index([modelName])
  @@index([timestamp])
}

model PasswordResetTokens {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String    @db.Uuid
  token     String    @unique @db.VarChar(255)
  expiresAt DateTime
  isUsed    Boolean   @default(false)
  createdAt DateTime  @default(now())
  usedAt    DateTime?
  user      Users     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([token])
  @@index([expiresAt])
  @@index([isUsed])
}

model CrossTimeframeInsights {
  id                 String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sourceCategory     String    @db.VarChar(20)
  targetCategory     String    @db.VarChar(20)
  insightType        String    @db.VarChar(50)
  insightContent     String
  confidence         Decimal   @db.Decimal(3, 2)
  applicabilityScore Decimal   @db.Decimal(3, 2)
  validationCount    Int       @default(0)
  successCount       Int       @default(0)
  marketCondition    String?   @db.VarChar(50)
  createdAt          DateTime  @default(now())
  lastValidated      DateTime?
  isActive           Boolean   @default(true)

  @@index([confidence, applicabilityScore])
  @@index([isActive, createdAt])
  @@index([sourceCategory, targetCategory])
}

model DataQualityMetrics {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  tableName      String   @db.VarChar(50)
  metricName     String   @db.VarChar(50)
  metricValue    Decimal? @db.Decimal(10, 4)
  thresholdMin   Decimal? @db.Decimal(10, 4)
  thresholdMax   Decimal? @db.Decimal(10, 4)
  status         String   @default("OK") @db.VarChar(20)
  checkTimestamp DateTime
  details        Json?
  createdAt      DateTime @default(now())

  @@index([status, checkTimestamp])
  @@index([tableName, checkTimestamp])
}

model DecisionOutcomes {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  decisionId       String   @unique @db.VarChar(100)
  symbolId         String   @db.Uuid
  action           String   @db.VarChar(10)
  expectedReturn   Decimal  @db.Decimal(8, 4)
  actualReturn     Decimal  @db.Decimal(8, 4)
  timeToTarget     Int
  riskRealized     Decimal  @db.Decimal(8, 4)
  userSatisfaction Decimal  @db.Decimal(3, 1)
  marketConditions Json
  confidence       Decimal  @db.Decimal(3, 2)
  reasoning        Json
  createdAt        DateTime @default(now())
  Symbols          Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([action])
  @@index([actualReturn])
  @@index([symbolId, createdAt])
}

model HistoricalData {
  id          String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId    String   @db.Uuid
  timeframe   String   @db.VarChar(10)
  timestamp   DateTime
  openPrice   Decimal  @db.Decimal(20, 8)
  highPrice   Decimal  @db.Decimal(20, 8)
  lowPrice    Decimal  @db.Decimal(20, 8)
  closePrice  Decimal  @db.Decimal(20, 8)
  volume      Decimal  @db.Decimal(20, 8)
  trades      Int?
  quoteVolume Decimal? @db.Decimal(20, 8)
  dataQuality Decimal  @default(1.0) @db.Decimal(3, 2)
  createdAt   DateTime @default(now())
  dataSource  String   @default("binance") @db.VarChar(20)
  isProtected Boolean  @default(true)
  updatedAt   DateTime @default(now())
  Symbols     Symbols  @relation(fields: [symbolId], references: [id])

  @@unique([symbolId, timeframe, timestamp])
  @@index([dataQuality])
  @@index([dataSource])
  @@index([isProtected])
  @@index([symbolId, timeframe, timestamp])
  @@index([symbolId, timestamp], map: "idx_historical_data_symbol_time")
}

model InvestmentDecisions {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId           String              @db.Uuid
  action             String              @db.VarChar(10)
  executionPlan      Json
  riskControls       Json
  expectedReturn     Decimal             @db.Decimal(8, 4)
  reasoning          String[]
  monitoringPoints   String[]
  adjustmentTriggers String[]
  tradingSignalId    String?             @db.Uuid
  riskAssessmentId   String?             @db.Uuid
  trendAnalysisId    String?             @db.Uuid
  userProfile        Json
  confidence         Decimal             @db.Decimal(3, 2)
  consistencyScore   Decimal             @db.Decimal(3, 2)
  status             String              @default("active") @db.VarChar(20)
  executedAt         DateTime?
  outcome            String?
  performanceMetrics Json?
  timestamp          DateTime
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now())
  AiReasoningChains  AiReasoningChains[]
  Symbols            Symbols             @relation(fields: [symbolId], references: [id], onDelete: Cascade)
  TradingSignals     TradingSignals?     @relation(fields: [tradingSignalId], references: [id])

  @@index([action, status])
  @@index([confidence])
  @@index([symbolId, timestamp])
}

model InvitationCodes {
  id                                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  code                                 String    @unique @db.VarChar(32)
  createdBy                            String?   @db.Uuid
  usedBy                               String?   @db.Uuid
  isUsed                               Boolean   @default(false)
  expiresAt                            DateTime?
  createdAt                            DateTime  @default(now())
  usedAt                               DateTime?
  usersInvitationCodesCreatedByToUsers Users?    @relation("InvitationCodes_createdByToUsers", fields: [createdBy], references: [id])
  usersInvitationCodesUsedByToUsers    Users?    @relation("InvitationCodes_usedByToUsers", fields: [usedBy], references: [id])

  @@index([code])
  @@index([createdBy])
  @@index([expiresAt])
  @@index([isUsed])
  @@index([usedBy])
}

model KeyLevelAnalysis {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId              String   @db.Uuid
  analysisTimestamp     DateTime @default(now())
  supportLevels         Json
  resistanceLevels      Json
  dynamicLevels         Json
  levelStrengths        Json
  levelReliability      Json
  recentBreakouts       Json?
  falseBreakouts        Json?
  tradingRanges         Json
  consolidationZones    Json?
  validUntil            DateTime
  aiLevelConfidence     Decimal  @db.Decimal(3, 2)
  aiBreakoutProbability Json?
  createdAt             DateTime @default(now())
  Symbols               Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([aiLevelConfidence])
  @@index([symbolId, analysisTimestamp])
  @@index([validUntil])
}

model KnowledgeEntities {
  id                                                              String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                                                            String                   @db.VarChar(100)
  type                                                            String                   @db.VarChar(50)
  domain                                                          String                   @db.VarChar(50)
  description                                                     String?
  properties                                                      Json
  confidence                                                      Decimal                  @default(0.8) @db.Decimal(3, 2)
  source                                                          String                   @default("system") @db.VarChar(50)
  isActive                                                        Boolean                  @default(true)
  createdAt                                                       DateTime                 @default(now())
  updatedAt                                                       DateTime                 @default(now())
  knowledgeRelationsKnowledgeRelationsSourceIdToKnowledgeEntities KnowledgeRelations[]     @relation("KnowledgeRelations_sourceIdToKnowledgeEntities")
  knowledgeRelationsKnowledgeRelationsTargetIdToKnowledgeEntities KnowledgeRelations[]     @relation("KnowledgeRelations_targetIdToKnowledgeEntities")
  KnowledgeSearchIndices                                          KnowledgeSearchIndices[]

  @@index([isActive])
  @@index([name])
  @@index([type, domain])
}

model KnowledgeRelations {
  id                                                             String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sourceId                                                       String            @db.Uuid
  targetId                                                       String            @db.Uuid
  relationType                                                   String            @db.VarChar(50)
  strength                                                       Decimal           @default(0.5) @db.Decimal(3, 2)
  direction                                                      String            @default("positive") @db.VarChar(20)
  timeDelay                                                      String?           @db.VarChar(20)
  conditions                                                     Json?
  confidence                                                     Decimal           @default(0.8) @db.Decimal(3, 2)
  evidence                                                       Json?
  isActive                                                       Boolean           @default(true)
  createdAt                                                      DateTime          @default(now())
  updatedAt                                                      DateTime          @default(now())
  knowledgeEntitiesKnowledgeRelationsSourceIdToKnowledgeEntities KnowledgeEntities @relation("KnowledgeRelations_sourceIdToKnowledgeEntities", fields: [sourceId], references: [id])
  knowledgeEntitiesKnowledgeRelationsTargetIdToKnowledgeEntities KnowledgeEntities @relation("KnowledgeRelations_targetIdToKnowledgeEntities", fields: [targetId], references: [id])

  @@index([relationType])
  @@index([sourceId])
  @@index([targetId])
}

model KnowledgeSearchIndices {
  id                String            @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  entityId          String            @db.Uuid
  content           String
  keywords          String
  domain            String            @db.VarChar(50)
  weight            Decimal           @default(1.0) @db.Decimal(3, 2)
  KnowledgeEntities KnowledgeEntities @relation(fields: [entityId], references: [id])

  @@index([domain])
  @@index([keywords])
}

model LearningEffectivenessLog {
  id                      String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  validationType          String   @db.VarChar(30)
  parameterKey            String?  @db.VarChar(50)
  beforeMetrics           Json
  afterMetrics            Json
  improvementScore        Decimal  @db.Decimal(5, 4)
  statisticalSignificance Decimal? @db.Decimal(4, 3)
  validationResult        String   @db.VarChar(20)
  actionTaken             String?  @db.VarChar(30)
  createdAt               DateTime @default(now())

  @@index([parameterKey, validationResult])
  @@index([validationType, createdAt])
}

model LearningKnowledgeBase {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  knowledgeType   String   @db.VarChar(30)
  knowledgeKey    String   @db.VarChar(100)
  knowledgeData   Json
  confidenceScore Decimal  @db.Decimal(3, 2)
  sampleCount     Int
  lastUpdated     DateTime
  createdAt       DateTime @default(now())

  @@unique([knowledgeType, knowledgeKey])
  @@index([knowledgeType, confidenceScore])
  @@index([lastUpdated])
}

model LongCycleMetrics {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId              String   @db.Uuid
  timeWindow            String   @db.VarChar(10)
  predictionType        String   @db.VarChar(20)
  accuracyRate          Decimal  @db.Decimal(5, 4)
  predictionCount       Int
  correctPredictions    Int
  confidenceCalibration Decimal? @db.Decimal(3, 2)
  improvementRate       Decimal? @db.Decimal(5, 4)
  averageConfidence     Decimal? @db.Decimal(3, 2)
  calculatedAt          DateTime
  createdAt             DateTime @default(now())
  Symbols               Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, timeWindow, predictionType, calculatedAt])
  @@index([accuracyRate])
  @@index([predictionType, calculatedAt])
  @@index([symbolId, timeWindow])
}

model LongCyclePredictions {
  id                     String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId               String    @db.Uuid
  predictionType         String    @db.VarChar(20)
  predictedValue         Decimal   @db.Decimal(20, 8)
  predictedDirection     String?   @db.VarChar(10)
  confidence             Decimal   @db.Decimal(3, 2)
  marketContext          Json
  modelVersion           String    @db.VarChar(20)
  predictionTimestamp    DateTime
  verificationTimestamp  DateTime?
  targetVerificationTime DateTime
  actualValue            Decimal?  @db.Decimal(20, 8)
  actualDirection        String?   @db.VarChar(10)
  accuracyScore          Decimal?  @db.Decimal(3, 2)
  isVerified             Boolean   @default(false)
  verificationJobId      String?   @db.VarChar(100)
  predictionHorizon      String    @default("8h") @db.VarChar(10)
  verificationDelay      String    @default("24h") @db.VarChar(10)
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @default(now())
  strategyType           String?   @default("swing") @db.VarChar(20)
  Symbols                Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, predictionTimestamp, predictionType])
  @@index([accuracyScore])
  @@index([confidence])
  @@index([predictionHorizon])
  @@index([predictionType, isVerified])
  @@index([symbolId, predictionTimestamp])
  @@index([targetVerificationTime])
  @@index([verificationTimestamp])
}

model MacroCycleMetrics {
  id                 String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId           String   @db.Uuid
  timeWindow         String   @db.VarChar(10)
  predictionType     String   @default("strategy_3d") @db.VarChar(20)
  accuracyRate       Decimal  @db.Decimal(5, 4)
  predictionCount    Int
  correctPredictions Int
  averageConfidence  Decimal  @db.Decimal(5, 4)
  trendAccuracy      Decimal? @db.Decimal(5, 4)
  directionAccuracy  Decimal? @db.Decimal(5, 4)
  calculatedAt       DateTime
  createdAt          DateTime @default(now())
  Symbols            Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, timeWindow, predictionType, calculatedAt])
  @@index([accuracyRate])
  @@index([predictionType, calculatedAt])
  @@index([symbolId, timeWindow])
}

model MacroCyclePredictions {
  id                     String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId               String    @db.Uuid
  predictionType         String    @default("strategy_3d") @db.VarChar(20)
  predictedValue         Decimal   @db.Decimal(20, 8)
  predictedDirection     String    @db.VarChar(10)
  confidence             Decimal   @db.Decimal(3, 2)
  marketContext          Json
  predictionTimestamp    DateTime
  targetVerificationTime DateTime
  predictionHorizon      String    @default("3d") @db.VarChar(10)
  verificationDelay      String    @default("7d") @db.VarChar(10)
  actualValue            Decimal?  @db.Decimal(20, 8)
  actualDirection        String?   @db.VarChar(10)
  verificationTimestamp  DateTime?
  accuracyScore          Decimal?  @db.Decimal(5, 4)
  isVerified             Boolean   @default(false)
  modelVersion           String    @db.VarChar(50)
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @default(now())
  strategyType           String?   @default("strategic") @db.VarChar(20)
  metadata               Json?
  Symbols                Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, predictionTimestamp, predictionType])
  @@index([accuracyScore])
  @@index([confidence])
  @@index([predictionHorizon])
  @@index([predictionType, isVerified])
  @@index([symbolId, predictionTimestamp])
  @@index([targetVerificationTime])
  @@index([verificationTimestamp])
}

model MacroLearningKnowledge {
  id                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  knowledgeType        String    @db.VarChar(20)
  marketCondition      String    @db.VarChar(50)
  knowledge            String
  confidence           Decimal   @db.Decimal(3, 2)
  validationCount      Int       @default(0)
  successRate          Decimal   @default(0) @db.Decimal(3, 2)
  applicableTimeframes Json?
  metadata             Json?
  createdAt            DateTime  @default(now())
  lastValidated        DateTime?

  @@index([confidence])
  @@index([knowledgeType])
  @@index([marketCondition])
  @@index([successRate])
}

model MacroPredictionValidations {
  id                        String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  predictionId              String   @db.Uuid
  validationType            String   @db.VarChar(20)
  validationScore           Decimal  @db.Decimal(5, 4)
  actualOutcome             Json?
  deviationAnalysis         Json?
  learningPoints            Json?
  adjustmentRecommendations Json?
  validatedAt               DateTime @default(now())

  @@index([predictionId])
  @@index([validationScore])
  @@index([validationType])
}

model MarketSentiment {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId         String   @db.Uuid
  fearGreedIndex   Int?
  socialSentiment  Decimal? @db.Decimal(3, 2)
  newsSentiment    Decimal? @db.Decimal(3, 2)
  fundingRate      Decimal? @db.Decimal(8, 6)
  openInterest     Decimal? @db.Decimal(20, 8)
  longShortRatio   Decimal? @db.Decimal(6, 4)
  whaleActivity    Json?
  exchangeInflow   Decimal? @db.Decimal(20, 8)
  exchangeOutflow  Decimal? @db.Decimal(20, 8)
  twitterMentions  Int      @default(0)
  redditSentiment  Decimal? @db.Decimal(3, 2)
  telegramActivity Int      @default(0)
  dataSource       String   @db.VarChar(20)
  dataQuality      Decimal  @default(1.0) @db.Decimal(3, 2)
  confidence       Decimal  @default(1.0) @db.Decimal(3, 2)
  timestamp        DateTime
  createdAt        DateTime @default(now())
  Symbols          Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, timestamp])
  @@index([dataQuality])
  @@index([fearGreedIndex])
  @@index([symbolId, timestamp])
}

model MfaAttempts {
  id            String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId        String         @db.Uuid
  deviceId      String?        @db.Uuid
  attemptType   MfaAttemptType
  code          String?        @db.VarChar(10)
  isSuccess     Boolean        @default(false)
  failureReason String?        @db.VarChar(255)
  ipAddress     String?        @db.VarChar(45)
  userAgent     String?        @db.VarChar(500)
  expiresAt     DateTime?
  verifiedAt    DateTime?
  createdAt     DateTime       @default(now())
  MfaDevices    MfaDevices?    @relation(fields: [deviceId], references: [id])
  Users         Users          @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([attemptType])
  @@index([createdAt])
  @@index([deviceId])
  @@index([expiresAt])
  @@index([isSuccess])
  @@index([userId])
}

model MfaDevices {
  id           String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId       String        @db.Uuid
  deviceType   MfaDeviceType
  deviceName   String        @db.VarChar(100)
  secretKey    String?       @db.VarChar(255)
  phoneNumber  String?       @db.VarChar(20)
  emailAddress String?       @db.VarChar(255)
  isActive     Boolean       @default(true)
  isVerified   Boolean       @default(false)
  lastUsedAt   DateTime?
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @default(now())
  MfaAttempts  MfaAttempts[]
  Users        Users         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([deviceType])
  @@index([isActive])
  @@index([isVerified])
  @@index([userId])
}

model ModelPerformanceMetrics {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  modelName             String   @db.VarChar(50)
  metricDate            DateTime @db.Date
  totalCalls            Int
  totalCost             Decimal  @db.Decimal(10, 4)
  averageResponseTimeMs Int
  averageQualityScore   Decimal? @db.Decimal(3, 2)
  successRate           Decimal? @db.Decimal(3, 2)
  errorRate             Decimal? @db.Decimal(3, 2)
  tokensPerSecond       Decimal? @db.Decimal(8, 2)
  costPerToken          Decimal? @db.Decimal(8, 6)
  costPerCall           Decimal? @db.Decimal(8, 6)
  cacheHitRate          Decimal? @db.Decimal(3, 2)
  cacheSavings          Decimal? @db.Decimal(10, 4)
  createdAt             DateTime @default(now())

  @@unique([modelName, metricDate])
  @@index([averageQualityScore])
  @@index([modelName, metricDate])
  @@index([totalCost])
}

model MultiTimeframeTrendAnalysis {
  id                      String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId                String   @db.Uuid
  analysisTimestamp       DateTime @default(now())
  timeframeTrends         Json
  directionalConsistency  Decimal  @db.Decimal(3, 2)
  strengthConsistency     Decimal  @db.Decimal(3, 2)
  timingConsistency       Decimal  @db.Decimal(3, 2)
  overallConsistency      Decimal  @db.Decimal(3, 2)
  dominantTrendDirection  String   @db.VarChar(20)
  dominantTrendStrength   Decimal  @db.Decimal(3, 2)
  dominantTrendTimeframe  String   @db.VarChar(10)
  dominantTrendConfidence Decimal  @db.Decimal(3, 2)
  timeframeWeights        Json
  conflictsDetected       Json?
  conflictResolution      Json?
  overallDirection        String   @db.VarChar(20)
  overallStrength         Decimal  @db.Decimal(3, 2)
  overallConfidence       Decimal  @db.Decimal(3, 2)
  createdAt               DateTime @default(now())
  Symbols                 Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([dominantTrendDirection, dominantTrendStrength])
  @@index([overallConsistency])
  @@index([overallDirection, overallStrength])
  @@index([symbolId, analysisTimestamp])
}

model OptimizationRecords {
  id                  String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  description         String
  optimizedParameters Json
  expectedImprovement Decimal  @db.Decimal(5, 4)
  implementationPlan  Json
  validationCriteria  Json
  createdAt           DateTime @default(now())

  @@index([createdAt])
  @@index([expectedImprovement])
}

model ParameterAdjustmentHistory {
  id                  String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  parameterKey        String    @db.VarChar(50)
  oldValue            Decimal   @db.Decimal(10, 6)
  newValue            Decimal   @db.Decimal(10, 6)
  adjustmentType      String    @db.VarChar(20)
  triggerReason       String
  expectedImprovement Decimal?  @db.Decimal(5, 4)
  actualImprovement   Decimal?  @db.Decimal(5, 4)
  validationStatus    String?   @db.VarChar(20)
  createdAt           DateTime  @default(now())
  validatedAt         DateTime?

  @@index([parameterKey, createdAt])
  @@index([validationStatus, createdAt])
}

model ParameterConfigs {
  id                String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  parameterKey      String   @db.VarChar(50)
  currentValue      Decimal  @db.Decimal(10, 6)
  defaultValue      Decimal  @db.Decimal(10, 6)
  minValue          Decimal? @db.Decimal(10, 6)
  maxValue          Decimal? @db.Decimal(10, 6)
  adjustmentRate    Decimal  @default(0.1) @db.Decimal(3, 2)
  lastUpdated       DateTime
  version           String   @db.VarChar(20)
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  scope             String?  @default("global") @db.VarChar(20)
  timeframeCategory String?  @default("universal") @db.VarChar(20)
  createdBy         String?  @db.VarChar(255)
  updatedBy         String?  @db.VarChar(255)

  @@unique([parameterKey, version])
  @@index([lastUpdated])
  @@index([parameterKey, isActive])
}

model ParameterOptimizations {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  parameter  String   @db.VarChar(50)
  adjustment Decimal  @db.Decimal(8, 6)
  reason     String
  createdAt  DateTime @default(now())

  @@index([parameter, createdAt])
}

model PriceData {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId         String    @db.Uuid
  price            Decimal   @db.Decimal(20, 8)
  change24h        Decimal   @db.Decimal(20, 8)
  changePercent24h Decimal   @db.Decimal(8, 4)
  volume24h        Decimal   @db.Decimal(20, 8)
  high24h          Decimal?  @db.Decimal(20, 8)
  low24h           Decimal?  @db.Decimal(20, 8)
  marketCap        Decimal?  @db.Decimal(30, 2)
  rsi14            Decimal?  @db.Decimal(8, 4)
  macdLine         Decimal?  @db.Decimal(20, 8)
  macdSignal       Decimal?  @db.Decimal(20, 8)
  macdHistogram    Decimal?  @db.Decimal(20, 8)
  supportLevel     Decimal?  @db.Decimal(20, 8)
  resistanceLevel  Decimal?  @db.Decimal(20, 8)
  fearGreedIndex   Int?
  socialSentiment  Decimal?  @db.Decimal(3, 2)
  bollingerUpper   Decimal?  @db.Decimal(20, 8)
  bollingerLower   Decimal?  @db.Decimal(20, 8)
  bollingerMiddle  Decimal?  @db.Decimal(20, 8)
  stochasticK      Decimal?  @db.Decimal(8, 4)
  stochasticD      Decimal?  @db.Decimal(8, 4)
  williamsR        Decimal?  @db.Decimal(8, 4)
  cci              Decimal?  @db.Decimal(8, 4)
  atr              Decimal?  @db.Decimal(20, 8)
  dataQuality      Decimal   @default(1.0) @db.Decimal(3, 2)
  dataFreshness    Int       @default(0)
  cacheKey         String?   @db.VarChar(100)
  cacheExpiresAt   DateTime?
  timestamp        DateTime
  createdAt        DateTime  @default(now())
  Symbols          Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, timestamp])
  @@index([cacheKey, cacheExpiresAt])
  @@index([dataQuality])
  @@index([symbolId, timestamp])
  @@index([symbolId, timestamp], map: "idx_price_data_symbol_timestamp")
  @@index([timestamp], map: "idx_price_data_timestamp")
}

model PromptTemplates {
  id                    String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name                  String       @db.VarChar(100)
  category              String       @db.VarChar(50)
  templateContent       String
  variables             Json
  version               String       @db.VarChar(20)
  usageCount            Int          @default(0)
  averageQuality        Decimal?     @db.Decimal(3, 2)
  averageCost           Decimal?     @db.Decimal(10, 6)
  averageResponseTimeMs Int?
  description           String?
  tags                  String[]     @default([])
  isActive              Boolean      @default(true)
  createdAt             DateTime     @default(now())
  updatedAt             DateTime     @default(now())
  AiCallLogs            AiCallLogs[]

  @@unique([name, version])
  @@index([averageQuality])
  @@index([category, isActive])
  @@index([usageCount])
}

model RealTimeTrendMonitoring {
  id                  String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId            String    @db.Uuid
  timeframe           String    @db.VarChar(10)
  monitoringStatus    String    @db.VarChar(20)
  lastUpdateTime      DateTime  @default(now())
  trendChangeDetected Boolean   @default(false)
  changeType          String?   @db.VarChar(30)
  changeSignificance  Decimal?  @db.Decimal(3, 2)
  changeDescription   String?
  currentDirection    String    @db.VarChar(20)
  currentStrength     Decimal   @db.Decimal(3, 2)
  currentConfidence   Decimal   @db.Decimal(3, 2)
  previousDirection   String?   @db.VarChar(20)
  previousStrength    Decimal?  @db.Decimal(3, 2)
  directionChangeTime DateTime?
  alertTriggered      Boolean   @default(false)
  alertLevel          String?   @db.VarChar(20)
  alertMessage        String?
  updateLatency       Int?
  analysisTime        Int?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @default(now())
  Symbols             Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([alertTriggered, alertLevel])
  @@index([monitoringStatus, lastUpdateTime])
  @@index([symbolId, timeframe, lastUpdateTime])
  @@index([trendChangeDetected, changeSignificance])
}

model RiskAssessments {
  id                String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId          String              @db.Uuid
  portfolioId       String              @db.Uuid
  riskLevel         String              @db.VarChar(20)
  riskScore         Decimal             @db.Decimal(5, 2)
  riskDescription   String?
  position          Json
  riskMetrics       Json
  keyRiskFactors    Json
  var95             Decimal             @db.Decimal(20, 8)
  var99             Decimal             @db.Decimal(20, 8)
  expectedShortfall Decimal             @db.Decimal(20, 8)
  recommendations   String[]
  alertThresholds   Json
  contingencyPlan   String[]
  confidence        Decimal             @db.Decimal(3, 2)
  assessedAt        DateTime
  validUntil        DateTime
  status            String              @default("active") @db.VarChar(20)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @default(now())
  userId            String              @db.Uuid
  AiReasoningChains AiReasoningChains[]
  TradingAccounts   TradingAccounts     @relation(fields: [portfolioId], references: [id], onDelete: Cascade)
  Symbols           Symbols             @relation(fields: [symbolId], references: [id], onDelete: Cascade)
  Users             Users               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([portfolioId, assessedAt])
  @@index([portfolioId, userId])
  @@index([riskLevel, assessedAt])
  @@index([status, validUntil])
  @@index([symbolId, assessedAt])
  @@index([userId])
}

model RiskEvents {
  id              String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountId       String          @db.Uuid
  eventType       String          @db.VarChar(50)
  severity        String          @db.VarChar(20)
  description     String?
  currentValue    Decimal?        @db.Decimal(20, 8)
  thresholdValue  Decimal?        @db.Decimal(20, 8)
  actionTaken     String?         @db.VarChar(100)
  resolved        Boolean         @default(false)
  createdAt       DateTime        @default(now())
  resolvedAt      DateTime?
  TradingAccounts TradingAccounts @relation(fields: [accountId], references: [id], onDelete: Cascade)

  @@index([accountId, eventType])
  @@index([createdAt])
  @@index([severity, resolved])
}

model SecurityEvents {
  id                String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId            String?               @db.Uuid
  eventType         SecurityEventType
  eventCategory     SecurityEventCategory
  severity          SecuritySeverity
  description       String
  ipAddress         String?               @db.VarChar(45)
  userAgent         String?               @db.VarChar(500)
  deviceFingerprint String?               @db.VarChar(255)
  geolocation       Json?
  threatScore       Decimal?              @db.Decimal(3, 2)
  riskLevel         RiskLevel?
  detectedThreats   Json?
  responseAction    String?               @db.VarChar(100)
  isResolved        Boolean               @default(false)
  resolvedAt        DateTime?
  createdAt         DateTime              @default(now())
  Users             Users?                @relation(fields: [userId], references: [id])

  @@index([createdAt])
  @@index([eventCategory])
  @@index([eventType])
  @@index([isResolved])
  @@index([riskLevel])
  @@index([severity])
  @@index([threatScore])
  @@index([userId])
}

model ShortCycleMetrics {
  id                    String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId              String   @db.Uuid
  timeWindow            String   @db.VarChar(10)
  predictionType        String   @db.VarChar(20)
  accuracyRate          Decimal  @db.Decimal(5, 4)
  predictionCount       Int
  correctPredictions    Int
  confidenceCalibration Decimal? @db.Decimal(3, 2)
  improvementRate       Decimal? @db.Decimal(5, 4)
  averageConfidence     Decimal? @db.Decimal(3, 2)
  calculatedAt          DateTime
  createdAt             DateTime @default(now())
  Symbols               Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, timeWindow, predictionType, calculatedAt])
  @@index([accuracyRate])
  @@index([predictionType, calculatedAt])
  @@index([symbolId, timeWindow])
}

model ShortCyclePredictions {
  id                     String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId               String    @db.Uuid
  predictionType         String    @db.VarChar(20)
  predictedValue         Decimal   @db.Decimal(20, 8)
  predictedDirection     String?   @db.VarChar(10)
  confidence             Decimal   @db.Decimal(3, 2)
  marketContext          Json
  modelVersion           String    @db.VarChar(20)
  predictionTimestamp    DateTime
  verificationTimestamp  DateTime?
  actualValue            Decimal?  @db.Decimal(20, 8)
  actualDirection        String?   @db.VarChar(10)
  accuracyScore          Decimal?  @db.Decimal(3, 2)
  isVerified             Boolean   @default(false)
  verificationJobId      String?   @db.VarChar(100)
  createdAt              DateTime  @default(now())
  updatedAt              DateTime  @default(now())
  predictionHorizon      String    @default("15m") @db.VarChar(10)
  targetVerificationTime DateTime
  verificationDelay      String    @default("30m") @db.VarChar(10)
  strategyType           String?   @default("intraday") @db.VarChar(20)
  Symbols                Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@unique([symbolId, predictionTimestamp, predictionType])
  @@index([accuracyScore])
  @@index([confidence])
  @@index([predictionHorizon])
  @@index([predictionType, isVerified])
  @@index([symbolId, predictionTimestamp])
  @@index([targetVerificationTime])
  @@index([verificationTimestamp])
}

model SignalPerformanceTracking {
  id                 String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  signalId           String         @db.Uuid
  actualOutcome      String?
  profitLoss         Decimal?       @db.Decimal(10, 4)
  accuracy           Decimal?       @db.Decimal(5, 4)
  executionTime      DateTime?
  closeTime          DateTime?
  performanceScore   Decimal?       @db.Decimal(3, 2)
  overallPerformance Decimal?       @db.Decimal(3, 2)
  status             String         @default("PENDING")
  trackingDetails    Json?
  createdAt          DateTime       @default(now())
  updatedAt          DateTime       @default(now())
  TradingSignals     TradingSignals @relation(fields: [signalId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([overallPerformance(sort: Desc)])
  @@index([signalId])
  @@index([status])
}

model SignalQualityAssessments {
  id                String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  signalId          String         @db.Uuid
  consistencyScore  Decimal        @db.Decimal(3, 2)
  strengthScore     Decimal        @db.Decimal(3, 2)
  timelinessScore   Decimal        @db.Decimal(3, 2)
  overallQuality    Decimal        @db.Decimal(3, 2)
  assessmentDetails Json?
  assessedAt        DateTime       @default(now())
  createdAt         DateTime       @default(now())
  TradingSignals    TradingSignals @relation(fields: [signalId], references: [id], onDelete: Cascade, onUpdate: NoAction)

  @@index([assessedAt(sort: Desc)])
  @@index([overallQuality(sort: Desc)], map: "idxSignalQualityAssessmentsOverallQuality")
  @@index([signalId], map: "idxSignalQualityAssessmentsSignalId")
}

model Symbols {
  id                             String                           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbol                         String                           @unique @db.VarChar(20)
  baseAsset                      String                           @db.VarChar(10)
  quoteAsset                     String                           @db.VarChar(10)
  status                         String                           @default("ACTIVE") @db.VarChar(20)
  isActive                       Boolean                          @default(true)
  exchange                       String                           @default("binance") @db.VarChar(20)
  metadata                       Json                             @default("{}")
  tradingRules                   Json                             @default("{}")
  createdAt                      DateTime                         @default(now())
  updatedAt                      DateTime                         @default(now())
  AiReasoningChains              AiReasoningChains[]
  DecisionOutcomes               DecisionOutcomes[]
  HistoricalData                 HistoricalData[]
  InvestmentDecisions            InvestmentDecisions[]
  KeyLevelAnalysis               KeyLevelAnalysis[]
  LongCycleMetrics               LongCycleMetrics[]
  LongCyclePredictions           LongCyclePredictions[]
  MacroCycleMetrics              MacroCycleMetrics[]
  MacroCyclePredictions          MacroCyclePredictions[]
  MarketSentiment                MarketSentiment[]
  MultiTimeframeTrendAnalysis    MultiTimeframeTrendAnalysis[]
  PriceData                      PriceData[]
  RealTimeTrendMonitoring        RealTimeTrendMonitoring[]
  RiskAssessments                RiskAssessments[]
  ShortCycleMetrics              ShortCycleMetrics[]
  ShortCyclePredictions          ShortCyclePredictions[]
  TechnicalPatternAnalysis       TechnicalPatternAnalysis[]
  tradingOrders                  TradingOrders[]
  tradingPositions               TradingPositions[]
  TradingSignals                 TradingSignals[]
  TrendAnalyses                  TrendAnalyses[]
  TrendAnalysisPerformance       TrendAnalysisPerformance[]
  TrendCollaborationIntelligence TrendCollaborationIntelligence[]
  WebsocketMessages              WebsocketMessages[]

  @@index([exchange])
  @@index([isActive])
  @@index([metadata])
  @@index([status])
}

model TechnicalPatternAnalysis {
  id                  String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId            String    @db.Uuid
  timeframe           String    @db.VarChar(10)
  patternType         String    @db.VarChar(50)
  patternCategory     String    @db.VarChar(30)
  patternStage        String    @db.VarChar(20)
  patternPoints       Json
  patternMeasurements Json
  confirmationLevel   Decimal   @db.Decimal(3, 2)
  volumeConfirmation  Boolean
  breakoutConfirmed   Boolean   @default(false)
  priceTargets        Json
  invalidationLevel   Decimal?  @db.Decimal(10, 2)
  formationStart      DateTime
  formationEnd        DateTime?
  completionTime      DateTime?
  accuracyScore       Decimal?  @db.Decimal(3, 2)
  outcome             String?   @db.VarChar(20)
  aiConfidence        Decimal   @db.Decimal(3, 2)
  aiEnhancedTargets   Json?
  aiRiskAssessment    Json?
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @default(now())
  Symbols             Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([accuracyScore])
  @@index([breakoutConfirmed, completionTime])
  @@index([patternStage, createdAt])
  @@index([patternType, confirmationLevel])
  @@index([symbolId, timeframe, createdAt])
}

model TimeframeConsistencyLogs {
  id                String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  checkTimestamp    DateTime @default(now())
  isConsistent      Boolean
  conflictCount     Int      @default(0)
  conflicts         Json?
  recommendations   Json?
  riskLevel         String   @db.VarChar(10)
  autoResolved      Boolean  @default(false)
  resolutionActions Json?

  @@index([checkTimestamp])
  @@index([riskLevel, isConsistent])
}

model TimeframeParameterConfigs {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  category       String   @db.VarChar(20)
  parameterKey   String   @db.VarChar(100)
  currentValue   Decimal  @db.Decimal(10, 6)
  defaultValue   Decimal  @db.Decimal(10, 6)
  minValue       Decimal? @db.Decimal(10, 6)
  maxValue       Decimal? @db.Decimal(10, 6)
  adjustmentRate Decimal  @default(0.1) @db.Decimal(3, 2)
  lastUpdated    DateTime @default(now())
  version        String   @db.VarChar(20)
  isActive       Boolean  @default(true)
  sampleCount    Int      @default(0)
  confidence     Decimal  @default(0.5) @db.Decimal(3, 2)
  createdAt      DateTime @default(now())

  @@unique([category, parameterKey, version])
  @@index([category, parameterKey])
  @@index([isActive, lastUpdated])
  @@index([version])
}

model TimeframeParameterHistory {
  id              String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  category        String   @db.VarChar(20)
  parameterKey    String   @db.VarChar(100)
  oldValue        Decimal  @db.Decimal(10, 6)
  newValue        Decimal  @db.Decimal(10, 6)
  triggerReason   String
  accuracy        Decimal? @db.Decimal(3, 2)
  marketCondition String?  @db.VarChar(50)
  metadata        Json?
  createdAt       DateTime @default(now())

  @@index([category])
  @@index([createdAt])
  @@index([parameterKey])
}

model TradingStatistics {
  id              String          @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountId       String          @db.Uuid
  date            DateTime        @db.Date
  totalTrades     Int             @default(0)
  winningTrades   Int             @default(0)
  losingTrades    Int             @default(0)
  totalPnl        Decimal         @default(0) @db.Decimal(20, 8)
  grossProfit     Decimal         @default(0) @db.Decimal(20, 8)
  grossLoss       Decimal         @default(0) @db.Decimal(20, 8)
  maxDrawdown     Decimal         @default(0) @db.Decimal(5, 4)
  winRate         Decimal         @default(0) @db.Decimal(5, 4)
  profitFactor    Decimal?        @db.Decimal(10, 4)
  sharpeRatio     Decimal?        @db.Decimal(10, 4)
  createdAt       DateTime        @default(now())
  TradingAccounts TradingAccounts @relation(fields: [accountId], references: [id], onDelete: Cascade)

  @@unique([accountId, date])
  @@index([accountId, date])
  @@index([winRate])
}

model TrendAnalyses {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId           String              @db.Uuid
  analysisTimeframe  String              @db.VarChar(10)
  direction          String              @db.VarChar(20)
  strength           Decimal             @db.Decimal(3, 2)
  persistence        String              @db.VarChar(20)
  keyLevels          Json
  reversalSignals    Json
  timeframeAlignment Json
  insights           String[]
  recommendations    String[]
  riskWarnings       String[]
  confidence         Decimal             @db.Decimal(3, 2)
  dataQuality        Decimal             @db.Decimal(3, 2)
  analysisVersion    String              @db.VarChar(20)
  timestamp          DateTime
  createdAt          DateTime            @default(now())
  AiReasoningChains  AiReasoningChains[]
  Symbols            Symbols             @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([analysisTimeframe, timestamp])
  @@index([confidence])
  @@index([direction, strength])
  @@index([symbolId, timestamp])
}

model TrendAnalysisPerformance {
  id                 String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId           String   @db.Uuid
  analysisType       String   @db.VarChar(30)
  executionTime      Int
  memoryUsage        Int?
  cpuUsage           Decimal? @db.Decimal(5, 2)
  accuracyScore      Decimal? @db.Decimal(3, 2)
  confidenceLevel    Decimal  @db.Decimal(3, 2)
  predictionAccuracy Decimal? @db.Decimal(3, 2)
  dataQuality        Decimal  @db.Decimal(3, 2)
  dataFreshness      Int
  dataCompleteness   Decimal  @db.Decimal(3, 2)
  systemLoad         Decimal? @db.Decimal(3, 2)
  concurrentRequests Int?
  timestamp          DateTime @default(now())
  Symbols            Symbols  @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([accuracyScore])
  @@index([analysisType, executionTime])
  @@index([symbolId, timestamp])
  @@index([timestamp])
}

model TrendCollaborationIntelligence {
  id                  String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  symbolId            String    @db.Uuid
  requestId           String    @unique @db.VarChar(50)
  requestType         String    @db.VarChar(30)
  requestingSystem    String    @db.VarChar(50)
  requestTimestamp    DateTime  @default(now())
  multiTimeframeTrend Json
  technicalPatterns   Json
  keyLevels           Json
  marketStructure     Json
  recommendations     Json
  riskAssessment      Json
  confidenceScore     Decimal   @db.Decimal(3, 2)
  responseTime        Int
  responseStatus      String    @db.VarChar(20)
  feedbackReceived    Boolean   @default(false)
  feedbackData        Json?
  feedbackTimestamp   DateTime?
  accuracyValidation  Decimal?  @db.Decimal(3, 2)
  learningUpdates     Json?
  createdAt           DateTime  @default(now())
  Symbols             Symbols   @relation(fields: [symbolId], references: [id], onDelete: Cascade)

  @@index([feedbackReceived, accuracyValidation])
  @@index([requestType, requestingSystem])
  @@index([responseStatus, responseTime])
  @@index([symbolId, requestTimestamp])
}

model UserLLMConfig {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId           String    @db.VarChar(255)
  providerName     String    @db.VarChar(50)
  apiKeyEncrypted  String
  isActive         Boolean   @default(true)
  configMetadata   Json      @default("{}")
  lastValidated    DateTime?
  validationStatus String?   @db.VarChar(20)
  errorMessage     String?
  totalCalls       Int       @default(0)
  totalCost        Decimal   @default(0) @db.Decimal(10, 6)
  lastUsed         DateTime?
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @default(now())

  @@index([isActive])
  @@index([providerName])
  @@index([userId])
}

model UserModelPreferences {
  id                String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId            String   @db.VarChar(255)
  scenario          String   @db.VarChar(100)
  preferredProvider String   @db.VarChar(50)
  preferredModel    String   @db.VarChar(100)
  fallbackProviders Json     @default("[]")
  customPrompt      String?
  temperature       Decimal? @db.Decimal(3, 2)
  maxTokens         Int?
  maxLatency        Int?
  maxCostPerCall    Decimal? @db.Decimal(8, 6)
  isActive          Boolean  @default(true)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @default(now())
  createdBy         String?  @db.VarChar(255)
  updatedBy         String?  @db.VarChar(255)

  @@index([isActive])
  @@index([scenario])
  @@index([userId])
}

model UserSessions {
  id         String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId     String   @db.Uuid
  tokenHash  String   @db.VarChar(255)
  deviceInfo String?  @db.VarChar(500)
  ipAddress  String?  @db.VarChar(45)
  userAgent  String?  @db.VarChar(500)
  expiresAt  DateTime
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  lastUsedAt DateTime @default(now())
  Users      Users    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([expiresAt])
  @@index([isActive])
  @@index([lastUsedAt])
  @@index([tokenHash])
  @@index([userId])
}

model Users {
  id                                             String                @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  username                                       String                @unique @db.VarChar(50)
  email                                          String                @unique @db.VarChar(255)
  passwordHash                                   String                @db.VarChar(255)
  role                                           UserRole              @default(USER)
  isActive                                       Boolean               @default(true)
  isEmailVerified                                Boolean               @default(false)
  lastLoginAt                                    DateTime?
  createdAt                                      DateTime              @default(now())
  updatedAt                                      DateTime              @default(now())
  backupCodes                                    Json?
  mfaEnabled                                     Boolean               @default(false)
  mfaSecret                                      String?               @db.VarChar(255)
  ApiKeys                                        ApiKeys[]
  invitationCodesInvitationCodesCreatedByToUsers InvitationCodes[]     @relation("InvitationCodes_createdByToUsers")
  invitationCodesInvitationCodesUsedByToUsers    InvitationCodes[]     @relation("InvitationCodes_usedByToUsers")
  MfaAttempts                                    MfaAttempts[]
  MfaDevices                                     MfaDevices[]
  PasswordResetTokens                            PasswordResetTokens[]
  RiskAssessments                                RiskAssessments[]
  SecurityEvents                                 SecurityEvents[]
  TradingAccounts                                TradingAccounts[]
  UserConfigHistory                              UserConfigHistory[]
  UserPreferences                                UserPreferences[]
  UserProfiles                                   UserProfiles?
  UserSessions                                   UserSessions[]

  @@index([email])
  @@index([isActive])
  @@index([lastLoginAt])
  @@index([mfaEnabled])
  @@index([role])
  @@index([username])
}

model WebsocketMessages {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  messageType      String   @db.VarChar(30)
  symbolId         String?  @db.Uuid
  messageData      Json
  confidence       Decimal? @db.Decimal(3, 2)
  dataQuality      Decimal? @db.Decimal(3, 2)
  triggerType      String   @db.VarChar(20)
  clientCount      Int      @default(0)
  processingTimeMs Int?
  timestamp        DateTime
  createdAt        DateTime @default(now())
  Symbols          Symbols? @relation(fields: [symbolId], references: [id])

  @@index([messageType, timestamp])
  @@index([symbolId, timestamp])
}

model DualTrackMonitoring {
  id             String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  accountId      String   @db.Uuid
  monitoringType String   @db.VarChar(20)
  metricName     String   @db.VarChar(50)
  metricValue    Decimal  @db.Decimal(20, 8)
  threshold      Decimal? @db.Decimal(20, 8)
  status         String   @db.VarChar(20)
  alertTriggered Boolean  @default(false)
  metadata       Json?
  timestamp      DateTime @default(now())

  @@index([accountId, monitoringType, timestamp])
  @@index([status, alertTriggered])
  @@index([timestamp])
}

model RiskAlerts {
  id             String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  type           String    @db.VarChar(50)
  severity       String    @db.VarChar(20)
  status         String    @db.VarChar(20)
  title          String    @db.VarChar(255)
  message        String
  portfolioId    String?   @db.VarChar(255)
  symbol         String?   @db.VarChar(20)
  triggeredAt    DateTime?
  resolvedAt     DateTime?
  acknowledgedAt DateTime?
  acknowledgedBy String?   @db.VarChar(255)
  resolvedBy     String?   @db.VarChar(255)
  metadata       Json?
  ruleId         String?   @db.VarChar(255)
  ruleName       String?   @db.VarChar(255)
  threshold      Decimal?  @db.Decimal(20, 8)
  actualValue    Decimal?  @db.Decimal(20, 8)
  actionRequired Boolean?  @default(false)
  tags           String[]  @default([])
  createdAt      DateTime  @default(now())
  updatedAt      DateTime

  @@index([actionRequired])
  @@index([portfolioId])
  @@index([ruleId])
  @@index([severity])
  @@index([status])
  @@index([symbol])
  @@index([triggeredAt])
  @@index([type])
}

model SignalFusionHistory {
  id               String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  createdAt        DateTime @default(now())
  fusionAlgorithm  String   @db.VarChar(50)
  fusionProcess    Json
  fusionWeights    Json
  inputSignals     Json
  processingTimeMs Int
  qualityMetrics   Json
  signalId         String   @db.Uuid

  @@index([signalId], map: "idx_signal_fusion_history_signal_id")
  @@index([createdAt(sort: Desc)])
  @@index([fusionAlgorithm])
  @@index([processingTimeMs])
  @@index([signalId])
}

model StrategySyncRecords {
  id                 String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sourceAccountId    String    @db.Uuid
  targetAccountId    String    @db.Uuid
  status             String    @db.VarChar(20)
  syncConfig         Json
  validationResult   Json?
  syncResult         Json?
  sourceParameters   Json?
  adjustedParameters Json?
  syncTimestamp      DateTime?
  createdAt          DateTime  @default(now())
  updatedAt          DateTime
  completedAt        DateTime?

  @@index([sourceAccountId, status])
  @@index([status, createdAt])
  @@index([targetAccountId, status])
}

model WebhookAlerts {
  id            String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name          String   @db.VarChar(100)
  webhookUrl    String
  triggerEvents String[]
  isActive      Boolean  @default(true)
  retryCount    Int      @default(3)
  timeout       Int      @default(5000)
  headers       Json?
  payload       Json?
  createdAt     DateTime @default(now())
  updatedAt     DateTime

  @@index([isActive])
  @@index([triggerEvents])
}

enum MfaAttemptType {
  SETUP
  VERIFY
  BACKUP
  RECOVERY
}

enum MfaDeviceType {
  TOTP
  SMS
  EMAIL
  HARDWARE_KEY
  BIOMETRIC
  BACKUP_CODES
}

enum RiskLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum SecurityEventCategory {
  AUTHENTICATION
  AUTHORIZATION
  AUDIT
  THREAT
  COMPLIANCE
}

enum SecurityEventType {
  LOGIN_SUCCESS
  LOGIN_FAILURE
  LOGOUT
  MFA_SETUP
  MFA_SUCCESS
  MFA_FAILURE
  PASSWORD_CHANGE
  ACCOUNT_LOCKED
  SUSPICIOUS_ACTIVITY
  THREAT_DETECTED
  PERMISSION_DENIED
  SESSION_EXPIRED
}

enum SecuritySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum UserRole {
  ADMIN
  USER
  GUEST
}
