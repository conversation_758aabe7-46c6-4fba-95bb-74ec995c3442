import { ContainerModule, interfaces } from 'inversify';
import { PrismaClient } from '@prisma/client';
import { RedisClientType } from 'redis';
import { TYPES } from '../types';
import { getGlobalPrismaClient } from '../../database/database';
import { getRedis, getRedisSubscriber, getRedisPublisher } from '../../messaging/redis';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { getMetrics } from '../../../../config/monitoring';
import { getEnvironmentManager, Environment } from '../../config/config-validation';
import { getLogger } from '../../../../config/logging';

/**
 * 基础设施容器模块
 * 修复模块耦合度违规：将DI容器拆分为专门的模块
 */
export const infrastructureContainerModule = new ContainerModule(async (bind: interfaces.Bind, unbind: interfaces.Unbind, isBound: interfaces.IsBound, rebind: interfaces.Rebind, unbindAsync: interfaces.UnbindAsync, onActivation: interfaces.OnActivation<any>, onDeactivation: interfaces.OnDeactivation<any>) => {
  // 环境配置 - 使用统一环境管理器
  bind<Environment>(TYPES.Environment).toConstantValue(getEnvironmentManager().getEnvironment());

  // 数据库
  bind<PrismaClient>(TYPES.Database).toDynamicValue(() => {
    return getGlobalPrismaClient();
  }).inSingletonScope();

  // Redis客户端
  bind<RedisClientType>(TYPES.Redis).toDynamicValue(() => {
    return getRedis();
  }).inSingletonScope();

  bind<RedisClientType>(TYPES.RedisSubscriber).toDynamicValue(() => {
    return getRedisSubscriber();
  }).inSingletonScope();

  bind<RedisClientType>(TYPES.RedisPublisher).toDynamicValue(() => {
    return getRedisPublisher();
  }).inSingletonScope();

  // 日志记录器 - 统一Logger绑定，避免Symbol冲突
  bind<IBasicLogger>(TYPES.Logger).toDynamicValue(() => {
    return getLogger('Application');
  }).inSingletonScope();

  // 监控指标
  bind(TYPES.Metrics).toDynamicValue(() => {
    return getMetrics();
  }).inSingletonScope();

  // HTTP客户端工厂
  const { HttpClientFactory } = await import('../../http/http-client-factory');
  bind(TYPES.HttpClientFactory).to(HttpClientFactory).inSingletonScope();
});

/**
 * 共享基础设施服务模块
 */
export const sharedInfrastructureModule = new ContainerModule(async (bind: interfaces.Bind) => {
  // 统一日志记录器
  const { UnifiedLogger } = await import('../../logging/unified-logger');
  

  // 事件总线
  const { EventBus } = await import('../../messaging/event-bus');
  bind(TYPES.Shared.EventBus).to(EventBus).inSingletonScope();

  // AI调用日志服务
  const { AICallLogService } = await import('../../ai/ai-call-log-service');
  bind(TYPES.Shared.AICallLogService).to(AICallLogService).inSingletonScope();

  // 统一AI服务管理器
  const { UnifiedAIServiceManager } = await import('../../ai/unified-ai-service-manager');
  bind(TYPES.Shared.UnifiedAIServiceManager).to(UnifiedAIServiceManager).inSingletonScope();

  // 统一分析服务管理器
  const { UnifiedAnalysisServiceManager } = await import('../../analysis/unified-analysis-service-manager');
  bind(TYPES.Shared.UnifiedAnalysisServiceManager).to(UnifiedAnalysisServiceManager).inSingletonScope();

  // 🔥 核心分析服务 - 修复DI容器集成问题
  // 动态权重分配服务
  const { DynamicWeightingService } = await import('../../analysis/services/DynamicWeightingService');
  bind(TYPES.Shared.DynamicWeightingService)
    .to(DynamicWeightingService)
    .inSingletonScope();

  // 模式识别服务
  const { PatternRecognitionService } = await import('../../analysis/services/PatternRecognitionService');
  const { IPatternRecognitionService } = await import('../../analysis/interfaces/IPatternRecognitionService');
  bind<IPatternRecognitionService>(TYPES.Shared.PatternRecognitionService)
    .to(PatternRecognitionService)
    .inSingletonScope();

  // 多时间框架服务
  const { MultiTimeframeService } = await import('../../analysis/services/MultiTimeframeService');
  const { IMultiTimeframeService } = await import('../../analysis/interfaces/IMultiTimeframeService');
  bind<IMultiTimeframeService>(TYPES.Shared.MultiTimeframeService)
    .to(MultiTimeframeService)
    .inSingletonScope();

  console.log('✅ 核心分析服务DI绑定已添加到共享基础设施模块');

  // Repository基础服务 - 移到共享模块确保所有仓储都能访问
  const { RepositoryBaseService } = await import('../../database/repository-base-service');
  bind(TYPES.Shared.RepositoryBaseService).to(RepositoryBaseService).inSingletonScope();

  // 数据映射服务 - 移到共享模块确保所有仓储都能访问
  const { DataMappingService } = await import('../../database/unified-data-mapper');
  bind(TYPES.Shared.DataMappingService).to(DataMappingService).inSingletonScope();

  // 查询管理器 - 移到共享模块确保RepositoryBaseService能访问
  const { QueryManager } = await import('../../database/query-manager');
  bind(TYPES.Shared.QueryManager).to(QueryManager).inSingletonScope();

  console.log('✅ 仓储基础服务已添加到共享基础设施模块');

  // 嵌入提供者
  const { OpenAIEmbeddingProvider, CohereEmbeddingProvider, LocalEmbeddingProvider } = await import('../../ai/vector-service');
  bind(TYPES.Shared.OpenAIEmbeddingProvider).to(OpenAIEmbeddingProvider).inSingletonScope();
  bind(TYPES.Shared.CohereEmbeddingProvider).to(CohereEmbeddingProvider).inSingletonScope();
  bind(TYPES.Shared.LocalEmbeddingProvider).to(LocalEmbeddingProvider).inSingletonScope();

  // 增强向量服务
  const { EnhancedVectorService } = await import('../../ai/vector-service');
  bind(TYPES.Shared.EnhancedVectorService).to(EnhancedVectorService).inSingletonScope();

  // 向量数据库服务
  const { VectorDatabaseService } = await import('../../ai/vector-database-service');
  bind(TYPES.Shared.VectorDatabaseService).to(VectorDatabaseService).inSingletonScope();

  // 多层缓存服务
  const { MultiTierCacheService } = await import('../../ai/multi-tier-cache-service');
  bind(TYPES.Shared.MultiTierCacheService).to(MultiTierCacheService).inSingletonScope();

  // 智能缓存管理器 - 使用MultiTierCacheService作为实现
  bind(TYPES.Shared.IntelligentCacheManager).toDynamicValue((context) => {
    return context.container.get(TYPES.Shared.MultiTierCacheService);
  }).inSingletonScope();

  // 统一语义缓存引擎
  const { SemanticCacheEngine, EnhancedSemanticCacheEngine } = await import('../../ai/semantic-cache-engine');
  bind(TYPES.Shared.SemanticCacheEngine).toDynamicValue((context) => {
    const logger = context.container.get<IBasicLogger>(TYPES.Logger);
    return new SemanticCacheEngine(logger);
  }).inSingletonScope();

  bind(TYPES.Shared.EnhancedSemanticCacheEngine).toDynamicValue((context) => {
    const logger = context.container.get<IBasicLogger>(TYPES.Logger);
    return new EnhancedSemanticCacheEngine(logger);
  }).inSingletonScope();

  // 动态缓存策略
  const { DynamicCacheStrategy } = await import('../../ai/dynamic-cache-strategy');
  bind(TYPES.Shared.DynamicCacheStrategy).to(DynamicCacheStrategy).inSingletonScope();

  // 智能AI调度器
  const { IntelligentAIScheduler } = await import('../../ai/ai-scheduler');
  bind(TYPES.Shared.IntelligentAIScheduler).to(IntelligentAIScheduler).inSingletonScope();

  // 请求合并器
  const { RequestMerger } = await import('../../ai/request-merger');
  bind(TYPES.Shared.RequestMerger).to(RequestMerger).inSingletonScope();

  // 优化集成AI服务 - 已替换为UnifiedAIServiceManager
  // const { OptimizedIntegratedAIService } = await import('../../ai/optimized-integrated-ai-service');
  // bind(TYPES.Shared.OptimizedIntegratedAIService).to(OptimizedIntegratedAIService).inSingletonScope();

  // 使用UnifiedAIServiceManager作为OptimizedIntegratedAIService的替代
  bind(TYPES.Shared.OptimizedIntegratedAIService).to(UnifiedAIServiceManager).inSingletonScope();
  console.log('✅ OptimizedIntegratedAIService 绑定到 UnifiedAIServiceManager 成功');

  // 优化实时推送服务
  try {
    const { RealtimePushService } = await import('../../ai/realtime-push-service');
    bind(TYPES.Shared.OptimizedRealtimePushService).to(RealtimePushService).inSingletonScope();

    // 将RealtimePushService也绑定到AIRealtimeService接口
    bind(TYPES.Shared.AIRealtimeService).to(RealtimePushService).inSingletonScope();
    console.log('✅ RealtimePushService 绑定成功');
  } catch (error) {
    console.error('❌ OptimizedRealtimePushService 绑定失败:', error);

    // 降级到简单实现
    class SimpleAIRealtimeService {
      async start() {
        console.log('SimpleAIRealtimeService started (fallback)');
      }
      async stop() {
        console.log('SimpleAIRealtimeService stopped (fallback)');
      }
    }
    bind(TYPES.Shared.AIRealtimeService).to(SimpleAIRealtimeService).inSingletonScope();
    console.log('⚠️ 使用SimpleAIRealtimeService作为降级方案');
  }

  // 增量分析服务已集成在UnifiedAnalysisServiceManager中，无需单独绑定
  // const { IncrementalAnalysisService } = await import('../../ai/incremental-analysis-service');
  // bind(TYPES.Shared.IncrementalAnalysisService).to(IncrementalAnalysisService).inSingletonScope();

  // 统一相似性服务
  const { UnifiedSimilarityService } = await import('../../ai/unified-similarity-service');
  bind(TYPES.Shared.UnifiedSimilarityService).to(UnifiedSimilarityService).inSingletonScope();

  // 统一错误处理器
  const { UnifiedErrorHandler } = await import('../../error/unified-error-handler');
  bind(TYPES.Shared.UnifiedErrorHandler).to(UnifiedErrorHandler).inSingletonScope();

  // 统一市场数据处理器
  const { UnifiedMarketDataProcessor } = await import('../../market-data/unified-market-data-processor');
  bind(TYPES.Shared.UnifiedMarketDataProcessor).to(UnifiedMarketDataProcessor).inSingletonScope();

  // 统一配置管理器
  const { UnifiedConfigManager } = await import('../../config/unified-config-manager');
  bind(TYPES.Shared.UnifiedConfigManager).to(UnifiedConfigManager).inSingletonScope();

  // 统一性能配置管理器
  const { UnifiedPerformanceConfigManager } = await import('../../config/unified-performance-config');
  bind(TYPES.Shared.UnifiedPerformanceConfigManager).to(UnifiedPerformanceConfigManager).inSingletonScope();

  // 统一环境变量管理器
  const { UnifiedEnvironmentManager } = await import('../../config/environment/unified-environment-manager');
  bind(TYPES.Shared.UnifiedEnvironmentManager).toConstantValue(UnifiedEnvironmentManager.getInstance());

  // 统一监控管理器
  const { UnifiedMonitoringManager } = await import('../../monitoring/unified-monitoring-manager');
  bind(TYPES.Shared.UnifiedMonitoringManager).to(UnifiedMonitoringManager).inSingletonScope();

  // 统一健康服务
  const { UnifiedHealthService } = await import('../../health/unified-health-service');
  bind(TYPES.Shared.UnifiedHealthService).to(UnifiedHealthService).inSingletonScope();

  // 配置新的统一监控服务
  // 注意：监控服务配置需要在容器初始化后单独处理
  // 在这里我们只导入服务，但不立即配置，避免容器未完全初始化的问题
  const { configureMonitoringServices } = await import('../../monitoring/di/monitoring-service-bindings');
  // 将configureMonitoringServices绑定到容器中，以便后续使用
  bind(TYPES.Shared.ConfigureMonitoringServices).toConstantValue(configureMonitoringServices);

  // 数据回填服务 - 应该在market-data-container-module中绑定，这里注释掉避免重复绑定
  // const { DataBackfillService } = await import('../../../../contexts/market-data/infrastructure/services/data-backfill-service');
  // bind(TYPES.MarketData.DataBackfillService).to(DataBackfillService).inSingletonScope();

  // 系统自动修复
  const { SystemAutoRepair } = await import('../../startup/system-auto-repair');
  bind(TYPES.Shared.SystemAutoRepair).to(SystemAutoRepair).inSingletonScope();

  // Express中间件 - 已修复DI绑定问题
  const { ExpressMiddleware } = await import('../../../../api/middleware/express-middleware');
  bind(TYPES.Shared.ExpressMiddleware).to(ExpressMiddleware).inSingletonScope();

  // 配置控制器 - 已修复DI绑定问题
  const { ConfigController } = await import('../../../../api/controllers/config-controller');
  bind(ConfigController).toSelf().inSingletonScope();

  // 增强缓存管理器 - 功能已整合到MultiTierCacheService中
  bind(TYPES.Shared.EnhancedCacheManager).to(MultiTierCacheService).inSingletonScope();

  // 并发优化器
  const { ConcurrencyOptimizer } = await import('../../performance/concurrency-optimizer');
  bind(TYPES.Shared.ConcurrencyOptimizer).to(ConcurrencyOptimizer).inSingletonScope();

  // 统一警报系统
  const { UnifiedAlertSystem } = await import('../../monitoring/unified-alert-system');
  bind(TYPES.Shared.UnifiedAlertSystem).to(UnifiedAlertSystem).inSingletonScope();

  // 日志聚合器
  const { LogAggregator } = await import('../../logging/log-aggregator');
  bind(TYPES.Shared.LogAggregator).to(LogAggregator).inSingletonScope();

  // 统一性能管理器
  const { UnifiedPerformanceManager } = await import('../../performance/unified-performance-manager');
  bind(TYPES.Shared.UnifiedPerformanceManager).to(UnifiedPerformanceManager).inSingletonScope();

  // 优化查询管理器
  const { QueryManager: OptimizedQueryManager } = await import('../../database/query-manager');
  bind(TYPES.Shared.OptimizedQueryManager).to(OptimizedQueryManager).inSingletonScope();

  // 统一技术指标计算器 - 重新启用
  const { UnifiedTechnicalIndicatorCalculator } = await import('../../technical-indicators/unified-technical-indicator-calculator');
  const { UNIFIED_TECHNICAL_INDICATOR_TYPES } = await import('../../technical-indicators/types');
  bind(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator)
    .to(UnifiedTechnicalIndicatorCalculator)
    .inSingletonScope();

  // 同时绑定到Shared命名空间以保持兼容性
  bind(TYPES.Shared.UnifiedTechnicalIndicatorCalculator)
    .to(UnifiedTechnicalIndicatorCalculator)
    .inSingletonScope();
  console.log('✅ 统一技术指标计算器绑定已重新启用（核心分析服务已在第75-92行绑定）');



  // 用户身份服务
  const { UserIdentityService } = await import('../../services/user-identity-service');
  bind(TYPES.Shared.UserIdentityService).to(UserIdentityService).inSingletonScope();

  // DTO映射器 - 解决问题19：实体到DTO映射逻辑重复
  const {
    PriceDataDtoMapper,
    KlineDataDtoMapper,
    MarketDataDtoMapperFactory
  } = await import('../../../application/dto-mappers/market-data-dto-mappers');

  const {
    TradingPositionDtoMapper,
    TradingExecutionDtoMapperFactory
  } = await import('../../../application/dto-mappers/trading-execution-dto-mappers');

  const {
    UnifiedDtoMapperRegistry
  } = await import('../../../application/dto-mappers/unified-dto-mapper-registry');

  // 注册具体映射器
  bind(TYPES.Shared.PriceDataDtoMapper).to(PriceDataDtoMapper).inSingletonScope();
  bind(TYPES.Shared.KlineDataDtoMapper).to(KlineDataDtoMapper).inSingletonScope();
  bind(TYPES.Shared.TradingPositionDtoMapper).to(TradingPositionDtoMapper).inSingletonScope();

  // 注册映射器工厂
  bind(TYPES.Shared.MarketDataDtoMapperFactory).to(MarketDataDtoMapperFactory).inSingletonScope();
  bind(TYPES.Shared.TradingExecutionDtoMapperFactory).to(TradingExecutionDtoMapperFactory).inSingletonScope();

  // 注册统一映射器注册表
  bind(TYPES.Shared.UnifiedDtoMapperRegistry).to(UnifiedDtoMapperRegistry).inSingletonScope();

  // 健康检查聚合器 - 解决问题20：分散的健康检查逻辑重复
  const { HealthCheckAggregator } = await import('../../health/health-check-aggregator');
  const { HealthCheckController } = await import('../../health/health-check-controller');
  const { AIServiceHealthProvider } = await import('../../health/providers/ai-service-health-provider');
  const { ExternalApiHealthProvider } = await import('../../health/providers/external-api-health-provider');
  const { TradingMonitoringHealthProvider } = await import('../../health/providers/trading-monitoring-health-provider');

  // 注册健康检查服务
  bind(TYPES.Shared.HealthCheckAggregator).to(HealthCheckAggregator).inSingletonScope();
  bind(TYPES.Shared.HealthCheckController).to(HealthCheckController).inSingletonScope();
  bind(TYPES.Shared.AIServiceHealthProvider).to(AIServiceHealthProvider).inSingletonScope();
  bind(TYPES.Shared.ExternalApiHealthProvider).to(ExternalApiHealthProvider).inSingletonScope();
  bind(TYPES.Shared.TradingMonitoringHealthProvider).to(TradingMonitoringHealthProvider).inSingletonScope();

  // 增强认证中间件
  const { EnhancedAuthMiddleware } = await import('../../auth/auth-middleware');
  bind(TYPES.Shared.EnhancedAuthMiddleware).to(EnhancedAuthMiddleware).inSingletonScope();

  // API监控服务已移除，改为手动执行模式
  // const { APIDiscoveryService } = await import('../../monitoring/api-discovery-service');
  // const { AutomatedAPIMonitor } = await import('../../monitoring/automated-api-monitor');
  // const { StartupAPIMonitor } = await import('../../monitoring/startup-api-monitor');

  // bind(TYPES.Shared.APIDiscoveryService).to(APIDiscoveryService).inSingletonScope();
  // bind(TYPES.Shared.AutomatedAPIMonitor).to(AutomatedAPIMonitor).inSingletonScope();
  // bind(TYPES.Shared.StartupAPIMonitor).to(StartupAPIMonitor).inSingletonScope();

  // 真实数据验证器
  const { RealDataValidator } = await import('../../validation/real-data-validator');
  bind(TYPES.Validation.RealDataValidator).to(RealDataValidator).inSingletonScope();

  // 价格验证引擎
  const { PriceValidationEngine } = await import('../../validation/price-validation-engine');
  bind(TYPES.Validation.PriceValidationEngine).to(PriceValidationEngine).inSingletonScope();

  // 预测验证中间件
  const { PredictionValidationMiddleware } = await import('../../validation/prediction-validation-middleware');
  bind(TYPES.Validation.PredictionValidationMiddleware).to(PredictionValidationMiddleware).inSingletonScope();

  // AI决策验证器
  const { AIDecisionValidator } = await import('../../validation/ai-decision-validator');
  bind(TYPES.Validation.AIDecisionValidator).to(AIDecisionValidator).inSingletonScope();

  // 统一数据同步协调器
  const { UnifiedDataSyncCoordinator } = await import('../../../../application/unified-data-sync-coordinator');
  bind(TYPES.Shared.UnifiedDataSyncCoordinator).to(UnifiedDataSyncCoordinator).inSingletonScope();

  // 缓存一致性和性能监控服务
  const { CacheConsistencyManager } = await import('../../cache/cache-consistency-manager');
  const { CachePerformanceMonitor } = await import('../../cache/cache-performance-monitor');
  bind(TYPES.Shared.CacheConsistencyManager).to(CacheConsistencyManager).inSingletonScope();
  bind(TYPES.Shared.CachePerformanceMonitor).to(CachePerformanceMonitor).inSingletonScope();

  // 数据质量监控服务
  const { EnhancedDataQualityMonitor } = await import('../../data-quality/unified-data-quality-monitor');
  const { DataQualityDashboard } = await import('../../data-quality/data-quality-dashboard');
  bind(TYPES.Shared.EnhancedDataQualityMonitor).to(EnhancedDataQualityMonitor).inSingletonScope();
  bind(TYPES.Shared.DataQualityDashboard).to(DataQualityDashboard).inSingletonScope();

  // 风险强制执行服务
  const { RiskEnforcementEngine } = await import('../../risk/risk-enforcement-engine');
  const { RealTimeRiskMonitor } = await import('../../risk/real-time-risk-monitor');
  bind(TYPES.Shared.RiskEnforcementEngine).to(RiskEnforcementEngine).inSingletonScope();
  bind(TYPES.Shared.RealTimeRiskMonitor).to(RealTimeRiskMonitor).inSingletonScope();

  // 订单生命周期管理服务
  const { OrderLifecycleManager } = await import('../../order/order-lifecycle-manager');
  const { OrderStatusSyncService } = await import('../../order/order-status-sync-service');
  bind(TYPES.Shared.OrderLifecycleManager).to(OrderLifecycleManager).inSingletonScope();
  bind(TYPES.Shared.OrderStatusSyncService).to(OrderStatusSyncService).inSingletonScope();

  // 交易执行监控服务
  const { EnhancedTradingExecutionMonitor } = await import('../../monitoring/enhanced-trading-execution-monitor');
  bind(TYPES.Shared.EnhancedTradingExecutionMonitor).to(EnhancedTradingExecutionMonitor).inSingletonScope();
});
