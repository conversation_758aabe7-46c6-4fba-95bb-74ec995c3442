/**
 * 开发环境配置管理器
 * 为开发环境提供智能的默认配置和模拟服务
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { TYPES } from '../di/types';

export interface DevelopmentConfig {
  enableMockServices: boolean;
  enableRealExternalAPIs: boolean;
  mockDataGeneration: boolean;
  performanceOptimizations: boolean;
  debugMode: boolean;
}

export interface MockServiceConfig {
  aiServices: {
    enabled: boolean;
    responseDelay: number;
    successRate: number;
  };
  exchangeAPIs: {
    enabled: boolean;
    responseDelay: number;
    mockPriceVariation: number;
  };
  externalData: {
    enabled: boolean;
    useStaticData: boolean;
    generateRandomData: boolean;
  };
}

@injectable()
export class DevelopmentConfigManager {
  private readonly isDevelopment: boolean;
  private readonly config: DevelopmentConfig;
  private readonly mockConfig: MockServiceConfig;

  constructor(
    @inject(TYPES.Logger) private logger: IBasicLogger
  ) {
    this.isDevelopment = process.env.NODE_ENV === 'development';
    this.config = this.initializeDevelopmentConfig();
    this.mockConfig = this.initializeMockConfig();
    
    if (this.isDevelopment) {
      this.logger.info('🔧 开发环境配置管理器已启用', {
        mockServices: this.config.enableMockServices,
        realAPIs: this.config.enableRealExternalAPIs,
        debugMode: this.config.debugMode
      });
    }
  }

  /**
   * 初始化开发环境配置
   */
  private initializeDevelopmentConfig(): DevelopmentConfig {
    return {
      enableMockServices: this.isDevelopment && !this.hasRealAPIKeys(),
      enableRealExternalAPIs: this.hasRealAPIKeys(),
      mockDataGeneration: this.isDevelopment,
      performanceOptimizations: true,
      debugMode: this.isDevelopment
    };
  }

  /**
   * 初始化模拟服务配置
   */
  private initializeMockConfig(): MockServiceConfig {
    return {
      aiServices: {
        enabled: this.config.enableMockServices,
        responseDelay: 100, // 100ms模拟延迟
        successRate: 0.95 // 95%成功率
      },
      exchangeAPIs: {
        enabled: this.config.enableMockServices,
        responseDelay: 50,
        mockPriceVariation: 0.02 // 2%价格波动
      },
      externalData: {
        enabled: this.config.enableMockServices,
        useStaticData: true,
        generateRandomData: false
      }
    };
  }

  /**
   * 检查是否有真实的API密钥
   */
  private hasRealAPIKeys(): boolean {
    const apiKeys = [
      process.env.GEMINI_API_KEY,
      process.env.OPENAI_API_KEY,
      process.env.ANTHROPIC_API_KEY,
      process.env.BINANCE_API_KEY
    ];

    return apiKeys.some(key => 
      key && 
      !key.includes('mock') && 
      !key.includes('dev_') && 
      !key.includes('your_') &&
      key.length > 10
    );
  }

  /**
   * 获取API密钥（如果是开发环境且没有真实密钥，返回模拟密钥）
   */
  getAPIKey(service: string): string | null {
    const envKey = process.env[`${service.toUpperCase()}_API_KEY`];
    
    if (!envKey || envKey.includes('your_')) {
      if (this.isDevelopment) {
        return `dev_mock_${service.toLowerCase()}_key`;
      }
      return null;
    }
    
    return envKey;
  }

  /**
   * 检查服务是否应该使用模拟模式
   */
  shouldUseMockService(service: string): boolean {
    if (!this.isDevelopment) {
      return false;
    }

    const apiKey = this.getAPIKey(service);
    return !apiKey || apiKey.includes('mock') || apiKey.includes('dev_');
  }

  /**
   * 获取开发环境配置
   */
  getDevelopmentConfig(): DevelopmentConfig {
    return { ...this.config };
  }

  /**
   * 获取模拟服务配置
   */
  getMockConfig(): MockServiceConfig {
    return { ...this.mockConfig };
  }

  /**
   * 获取数据库配置（开发环境优化）
   */
  getDatabaseConfig() {
    if (!this.isDevelopment) {
      return {};
    }

    return {
      connectionLimit: 10, // 开发环境限制连接数
      queryTimeout: 30000,
      enableQueryLogging: true,
      enableSlowQueryLogging: true,
      slowQueryThreshold: 1000
    };
  }

  /**
   * 获取性能配置（开发环境优化）
   */
  getPerformanceConfig() {
    return {
      enableCaching: true,
      cacheSize: this.isDevelopment ? 100 : 1000,
      cacheTTL: this.isDevelopment ? 60000 : 300000, // 开发环境短缓存
      maxConcurrentTasks: this.isDevelopment ? 5 : 20,
      enableMetrics: true,
      enableProfiling: this.isDevelopment
    };
  }

  /**
   * 获取日志配置
   */
  getLoggingConfig() {
    return {
      level: this.isDevelopment ? 'debug' : 'info',
      enableConsoleOutput: this.isDevelopment,
      enableFileOutput: !this.isDevelopment,
      enableStructuredLogging: true,
      maxLogSize: this.isDevelopment ? '10MB' : '100MB'
    };
  }

  /**
   * 验证配置完整性
   */
  validateConfiguration(): {
    isValid: boolean;
    warnings: string[];
    errors: string[];
  } {
    const warnings: string[] = [];
    const errors: string[] = [];

    // 检查关键配置
    if (this.isDevelopment && !this.config.enableMockServices && !this.config.enableRealExternalAPIs) {
      warnings.push('开发环境中既未启用模拟服务也未配置真实API密钥');
    }

    if (!this.isDevelopment && this.config.enableMockServices) {
      errors.push('生产环境不应启用模拟服务');
    }

    // 检查安全配置
    const jwtSecret = process.env.JWT_SECRET;
    if (!this.isDevelopment && (!jwtSecret || jwtSecret.includes('dev-') || jwtSecret.length < 32)) {
      errors.push('生产环境必须配置强JWT密钥');
    }

    return {
      isValid: errors.length === 0,
      warnings,
      errors
    };
  }

  /**
   * 打印配置摘要
   */
  printConfigurationSummary(): void {
    if (!this.isDevelopment) {
      return;
    }

    const validation = this.validateConfiguration();
    
    this.logger.info('📋 开发环境配置摘要', {
      environment: process.env.NODE_ENV,
      mockServices: this.config.enableMockServices,
      realAPIs: this.config.enableRealExternalAPIs,
      hasRealAPIKeys: this.hasRealAPIKeys(),
      performanceOptimizations: this.config.performanceOptimizations,
      debugMode: this.config.debugMode
    });

    if (validation.warnings.length > 0) {
      this.logger.warn('⚠️ 配置警告', { warnings: validation.warnings });
    }

    if (validation.errors.length > 0) {
      this.logger.error('❌ 配置错误', { errors: validation.errors });
    }
  }
}
