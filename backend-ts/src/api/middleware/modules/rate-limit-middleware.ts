/**
 * API限流中间件
 * 提供全局和端点特定的限流功能
 */

import rateLimit from 'express-rate-limit';
import { Request, Response, NextFunction } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../shared/infrastructure/di/types/index';
import { IBasicLogger } from '../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { getUnifiedLoggerFeatures } from '../../../shared/infrastructure/logging/unified-logger';

/**
 * 限流配置接口
 */
interface RateLimitConfig {
  windowMs: number;
  max: number;
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
}

/**
 * 端点特定限流配置
 */
interface EndpointRateLimits {
  [path: string]: RateLimitConfig;
}

/**
 * 创建限流中间件
 */
export function createRateLimitMiddleware(container: Container) {
  const logger = getUnifiedLoggerFeatures(
    container.get<IBasicLogger>(TYPES.Logger)
  ).createComponentLogger('RateLimitMiddleware');

  // 全局限流配置
  const globalRateLimit = rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 100, // 每分钟最多100个请求
    message: {
      error: 'Too many requests',
      message: 'Rate limit exceeded. Please try again later.',
      retryAfter: 60
    },
    standardHeaders: true,
    legacyHeaders: false,
    keyGenerator: (req: Request) => {
      // 优先使用用户ID，其次使用IP
      const userReq = req as any;
      return userReq.user?.id || userReq.apiKey?.userId || req.ip;
    },
    handler: (req: Request, res: Response) => {
      logger.warn('全局限流触发', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent')
      });

      res.status(429).json({
        success: false,
        error: 'Too many requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: 60,
        timestamp: new Date().toISOString()
      });
    }
  });

  // 端点特定限流配置
  const endpointLimits: EndpointRateLimits = {
    // AI相关端点 - 更严格的限制
    '/v1/ai/analyze': {
      windowMs: 60 * 1000,
      max: 5, // 每分钟5次AI分析（测试用）
      message: 'AI analysis rate limit exceeded'
    },
    '/v1/ai/reasoning': {
      windowMs: 60 * 1000,
      max: 15, // 每分钟15次推理
      message: 'AI reasoning rate limit exceeded'
    },
    '/v1/ai/decision': {
      windowMs: 60 * 1000,
      max: 30, // 每分钟30次决策查询
      message: 'AI decision rate limit exceeded'
    },

    // 交易相关端点 - 中等限制
    '/v1/trading-signals': {
      windowMs: 60 * 1000,
      max: 50, // 每分钟50次信号查询
      message: 'Trading signals rate limit exceeded'
    },
    '/v1/trading-execution': {
      windowMs: 60 * 1000,
      max: 10, // 每分钟10次交易执行
      message: 'Trading execution rate limit exceeded'
    },

    // 市场数据端点 - 较宽松的限制
    '/v1/market-data': {
      windowMs: 60 * 1000,
      max: 200, // 每分钟200次市场数据查询
      message: 'Market data rate limit exceeded'
    },

    // 认证端点 - 严格限制
    '/v1/auth/login': {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 5, // 15分钟内最多5次登录尝试
      message: 'Too many login attempts',
      skipSuccessfulRequests: true
    },
    '/v1/auth/register': {
      windowMs: 60 * 60 * 1000, // 1小时
      max: 3, // 1小时内最多3次注册
      message: 'Too many registration attempts'
    }
  };

  // 预创建所有端点限流器实例
  const endpointLimiters = new Map<string, any>();

  /**
   * 创建端点特定的限流中间件
   */
  function createEndpointRateLimit(path: string, config: RateLimitConfig) {
    return rateLimit({
      windowMs: config.windowMs,
      max: config.max,
      message: {
        error: 'Rate limit exceeded',
        message: config.message || 'Too many requests for this endpoint',
        retryAfter: Math.ceil(config.windowMs / 1000)
      },
      standardHeaders: true,
      legacyHeaders: false,
      skipSuccessfulRequests: config.skipSuccessfulRequests || false,
      skipFailedRequests: config.skipFailedRequests || false,
      keyGenerator: config.keyGenerator || ((req: Request) => {
        const userReq = req as any;
        return userReq.user?.id || userReq.apiKey?.userId || req.ip;
      }),
      handler: (req: Request, res: Response) => {
        logger.warn('端点限流触发', {
          endpoint: path,
          ip: req.ip,
          path: req.path,
          method: req.method,
          limit: config.max,
          window: config.windowMs
        });

        res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          message: config.message || 'Too many requests for this endpoint',
          endpoint: path,
          retryAfter: Math.ceil(config.windowMs / 1000),
          timestamp: new Date().toISOString()
        });
      }
    });
  }

  // 初始化所有端点限流器
  for (const [path, config] of Object.entries(endpointLimits)) {
    endpointLimiters.set(path, createEndpointRateLimit(path, config));
    logger.info('端点限流器已创建', { path, max: config.max, windowMs: config.windowMs });
  }

  /**
   * 智能限流中间件 - 根据路径动态应用限制
   */
  function smartRateLimit(req: Request, res: Response, next: NextFunction) {
    logger.debug('智能限流检查', { path: req.path, method: req.method });

    // 检查是否有匹配的端点限制
    for (const [path, limiter] of endpointLimiters.entries()) {
      if (req.path.startsWith(path)) {
        logger.debug('应用端点限流', { endpoint: path, requestPath: req.path });
        return limiter(req, res, next);
      }
    }

    // 如果没有特定限制，应用全局限制
    logger.debug('应用全局限流', { path: req.path });
    return globalRateLimit(req, res, next);
  }

  /**
   * 基于IP的严格限流（用于可疑活动）
   */
  const strictIPRateLimit = rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 10, // 每分钟最多10个请求
    keyGenerator: (req: Request) => req.ip,
    message: {
      error: 'Strict rate limit exceeded',
      message: 'Your IP has been temporarily restricted due to suspicious activity',
      retryAfter: 60
    },
    handler: (req: Request, res: Response) => {
      logger.error('严格IP限流触发', {
        ip: req.ip,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent')
      });

      res.status(429).json({
        success: false,
        error: 'Strict rate limit exceeded',
        message: 'Your IP has been temporarily restricted due to suspicious activity',
        retryAfter: 60,
        timestamp: new Date().toISOString()
      });
    }
  });

  /**
   * 健康检查端点的宽松限流
   */
  const healthCheckRateLimit = rateLimit({
    windowMs: 60 * 1000, // 1分钟
    max: 1000, // 每分钟最多1000次健康检查
    keyGenerator: (req: Request) => req.ip,
    skipSuccessfulRequests: true,
    skipFailedRequests: true
  });

  return {
    // 全局限流
    global: globalRateLimit,
    
    // 智能限流（推荐使用）
    smart: smartRateLimit,
    
    // 严格IP限流
    strictIP: strictIPRateLimit,
    
    // 健康检查限流
    healthCheck: healthCheckRateLimit,
    
    // 创建自定义端点限流
    createEndpointLimit: createEndpointRateLimit,
    
    // 获取端点限流配置
    getEndpointLimits: () => endpointLimits,
    
    // 更新端点限流配置
    updateEndpointLimit: (path: string, config: RateLimitConfig) => {
      endpointLimits[path] = config;
      logger.info('端点限流配置已更新', { path, config });
    }
  };
}
